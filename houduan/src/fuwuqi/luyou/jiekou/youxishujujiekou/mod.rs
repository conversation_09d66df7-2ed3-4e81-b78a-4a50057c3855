#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

mod jiekou_guaiwushuju;
mod jiekou_guaiwushuju_rizhi;
mod jiekou_wupinshuju;
mod jiekou_wupinshuju_rizhi;
mod jiekou_jinengshuju;
mod jiekou_jinengshuju_rizhi;
pub mod jiekou_ditushuju;
pub mod jiekou_ditushuju_rizhi;

use crate::fuwuqi::luyou::jiekou_dingyii;
use rocket::Route;

/// 获取游戏数据接口模块的所有路由
pub fn get_routes() -> Vec<Route> {
    let mut luyou = Vec::new();

    // 添加怪物数据查询接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_quanbu_xinxi::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_ziduan_xinxi::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_shanchu_guaiwu_huancun::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_suoyou_guaiwu_huancun::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_liebiao::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_liebiao_huancun::get_routes());

    // 添加怪物名字查询接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_leiming_jingque::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_jingque::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_mohu::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_routes());

    // 添加怪物分类列表查询接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_fenlei_liebiao::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_fenlei_liebiao_huancun::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_fenlei_leixing_liebiao::get_routes());

    // 添加怪物联合搜索接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_lianhe_sousuo::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_lianhe_sousuo_huancun::get_routes());

    // 添加物品数据查询接口路由
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_quanbu_xinxi::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_ziduan_xinxi::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_shanchu_wupin_huancun::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_qingchu_suoyou_wupin_huancun::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_liebiao::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_qingchu_wupin_liebiao_huancun::get_routes());

    // 添加物品名字查询接口路由
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_mingzi_leiming_jingque::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_mingzi_jingque::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_mingzi_mohu::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_qingchu_wupin_mingzi_chaxun_huancun::get_routes());

    // 添加物品分类接口路由
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_fenlei_leixing::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_fenlei_zileixing::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_wanzheng_fenlei::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_leixing_liebiao::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_leixing_zileixing_liebiao::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_qingchu_wupin_fenlei_huancun::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_qingchu_wupin_fenlei_liebiao_huancun::get_routes());

    // 添加物品分类和名字联合搜索接口路由
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_lianhe_chaxun_leixing_mingzi::get_routes());
    luyou.extend(jiekou_wupinshuju::jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi::get_routes());

    // 添加技能数据查询接口路由
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_quanbu_xinxi::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_ziduan_xinxi::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_liebiao::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_qingchu_jineng_liebiao_huancun::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_leiming_jingque::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_mingzi_jingque::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_jineng_mingzi_mohu::get_routes());
    luyou.extend(jiekou_jinengshuju::jiekou_qingchu_jineng_mingzi_chaxun_huancun::get_routes());

    // 添加地图数据查询接口路由
    luyou.extend(jiekou_ditushuju::get_routes());

    luyou
}

/// 获取游戏数据接口模块的所有接口信息
pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    let mut jiekou_xinxi_liebiao = vec![
        jiekou_guaiwushuju::jiekou_guaiwu_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_shanchu_guaiwu_huancun::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_suoyou_guaiwu_huancun::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_liebiao::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_liebiao_huancun::get_jiekou_xinxi(),
        // 怪物名字查询接口信息
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_leiming_jingque::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_jingque::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_mohu::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_jiekou_xinxi(),
        // 怪物分类列表查询接口信息
        jiekou_guaiwushuju::jiekou_guaiwu_fenlei_liebiao::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_fenlei_liebiao_huancun::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_fenlei_leixing_liebiao::get_jiekou_xinxi(),
        // 怪物联合搜索接口信息
        jiekou_guaiwushuju::jiekou_guaiwu_lianhe_sousuo::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_lianhe_sousuo_huancun::get_jiekou_xinxi(),
        // 物品数据查询接口信息
        jiekou_wupinshuju::jiekou_wupin_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_shanchu_wupin_huancun::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_qingchu_suoyou_wupin_huancun::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_liebiao::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_qingchu_wupin_liebiao_huancun::get_jiekou_xinxi(),
        // 物品名字查询接口信息
        jiekou_wupinshuju::jiekou_wupin_mingzi_leiming_jingque::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_mingzi_jingque::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_mingzi_mohu::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_qingchu_wupin_mingzi_chaxun_huancun::get_jiekou_xinxi(),
        // 物品分类接口信息
        jiekou_wupinshuju::jiekou_wupin_fenlei_leixing::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_fenlei_zileixing::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_wanzheng_fenlei::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_leixing_liebiao::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_leixing_zileixing_liebiao::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_qingchu_wupin_fenlei_huancun::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_qingchu_wupin_fenlei_liebiao_huancun::get_jiekou_xinxi(),
        // 物品分类和名字联合搜索接口信息
        jiekou_wupinshuju::jiekou_wupin_lianhe_chaxun_leixing_mingzi::get_jiekou_xinxi(),
        jiekou_wupinshuju::jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi::get_jiekou_xinxi(),
        // 技能数据查询接口信息
        jiekou_jinengshuju::jiekou_jineng_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_jineng_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_jineng_liebiao::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_qingchu_jineng_liebiao_huancun::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_jineng_leiming_jingque::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_jineng_mingzi_jingque::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_jineng_mingzi_mohu::get_jiekou_xinxi(),
        jiekou_jinengshuju::jiekou_qingchu_jineng_mingzi_chaxun_huancun::get_jiekou_xinxi(),
    ];
    jiekou_xinxi_liebiao.extend(jiekou_ditushuju::get_jiekou_xinxi());
    jiekou_xinxi_liebiao
}