#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupin_redis_kongzhi::wupin_redis_kongzhi;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::{fenye_canshu, wupin_ziduan_yingshe};
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinshujuchuli::wupin_shuju_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinshuju_liebiao_chaxun::wupin_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupin_mingzi_chaxun::{mingzi_chaxun_tiaojian, wupin_mingzi_chaxun_guanli};
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinfenleishuchu::wupin_fenlei_shuchu_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupin_fenleiliebiao_chaxun::wupin_fenlei_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupin_mingzi_chaxun::mingzi_chaxun_leixing;
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;


/// 辅助函数：创建物品数据管理器（带Redis缓存支持）
fn chuangjian_wupin_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> wupin_shuju_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        let redis_kongzhi = wupin_redis_kongzhi::new(redis_state.as_ref().clone());
        wupin_shuju_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_kongzhi)
    } else {
        // 没有Redis，创建不带缓存的管理器
        wupin_shuju_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建物品名字查询管理器（带Redis缓存支持）
fn chuangjian_wupin_mingzi_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> wupin_mingzi_chaxun_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        wupin_mingzi_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        // 没有Redis，创建不带缓存的管理器
        wupin_mingzi_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建物品分类输出管理器（带Redis缓存支持）
fn chuangjian_wupin_fenlei_shuchu_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> wupin_fenlei_shuchu_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        wupin_fenlei_shuchu_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        // 没有Redis，创建不带缓存的管理器
        wupin_fenlei_shuchu_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建物品分类列表查询管理器（带Redis缓存支持）
fn chuangjian_wupin_fenlei_liebiao_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> wupin_fenlei_liebiao_chaxun_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        wupin_fenlei_liebiao_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        // 没有Redis，创建不带缓存的管理器
        wupin_fenlei_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 查询物品全部信息接口
#[get("/youxishuju/wupin/xinxi/<id>")]
pub async fn chaxun_wupin_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品数据管理器（带Redis缓存）
    let wupin_guanli = chuangjian_wupin_guanli(mysql_guanli, redis_guanli);

    // 查询物品全部信息
    match wupin_guanli.tongyong_chaxun(&id, wupin_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_wupin_quanbu_xinxi(&id),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_quanbu_xinxi(&id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_wupin(&id))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_wupin(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/xinxi/<_id>")]
pub fn chaxun_wupin_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

/// 查询物品指定字段信息接口
#[get("/youxishuju/wupin/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_wupin_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证字段名是否有效
    if !wupin_ziduan_yingshe::jiancha_ziduan_youxiao(&ziduan_ming) {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::wuxiao_ziduan_ming(&ziduan_ming)
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建物品数据管理器（带Redis缓存）
    let wupin_guanli = chuangjian_wupin_guanli(mysql_guanli, redis_guanli);

    // 查询物品指定字段信息
    match wupin_guanli.tongyong_chaxun(&id, &ziduan_ming).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_wupin_ziduan_xinxi(&id, &ziduan_ming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_ziduan_xinxi(&id, &ziduan_ming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_wupin_ziduan(&id, &ziduan_ming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_wupin_ziduan(&id, &ziduan_ming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_wupin_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

/// 删除物品缓存接口
#[delete("/youxishuju/wupin/huancun/<id>")]
pub async fn shanchu_wupin_huancun(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_shanchu_wupin_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_shanchu_wupin_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_shanchu_wupin_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品数据管理器（带Redis缓存）
    let wupin_guanli = chuangjian_wupin_guanli(mysql_guanli, redis_guanli);

    // 删除物品缓存
    match wupin_guanli.shanchu_wupin_huancun(&id).await {
        Ok(shanchu_chenggong) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_chenggong {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_shanchu_wupin_huancun(&id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_shanchu_wupin_huancun(&id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::wupin_huancun_buxunzai_huo_redis_weiqiyong(&id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_shanchu_wupin_huancun(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/huancun/<_id>")]
pub fn shanchu_wupin_huancun_yujian(_id: String) -> Status {
    Status::Ok
}

/// 清理所有物品缓存接口
#[delete("/youxishuju/wupin/huancun")]
pub async fn qingchu_suoyou_wupin_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_suoyou_wupin_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_suoyou_wupin_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_suoyou_wupin_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品数据管理器（带Redis缓存）
    let wupin_guanli = chuangjian_wupin_guanli(mysql_guanli, redis_guanli);

    // 清理所有物品缓存
    match wupin_guanli.qingchu_wupin_quanbu_xinxi_huancun().await {
        Ok(shanchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_shuliang > 0 {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_wupin_huancun(shanchu_shuliang as u32),
                    serde_json::json!({"shanchu_shuliang": shanchu_shuliang}),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_suoyou_wupin_huancun());
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::meiyou_xuyao_qingchu_huancun(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_qingchu_suoyou_wupin_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/huancun")]
pub fn qingchu_suoyou_wupin_huancun_yujian() -> Status {
    Status::Ok
}

/// 查询物品列表接口（分页）
#[get("/youxishuju/wupin/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_wupin_liebiao(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema < 1 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "页码必须大于等于1".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang < 1 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "每页数量必须在1-100之间".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema, meiye_shuliang);

    // 创建物品列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        let redis_lianjie = redis_state.as_ref().clone();
        wupin_liebiao_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_lianjie)
    } else {
        wupin_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 查询物品列表
    match liebiao_guanli.huoqu_wupin_liebiao(&fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_wupin_liebiao(yema, jieguo.wupin_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_liebiao(yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_wupin_liebiao())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_wupin_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/liebiao")]
pub fn chaxun_wupin_liebiao_yujian() -> Status {
    Status::Ok
}

/// 清除物品列表缓存接口
#[delete("/youxishuju/wupin/liebiao/huancun")]
pub async fn qingchu_wupin_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_wupin_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_wupin_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_wupin_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        let redis_lianjie = redis_state.as_ref().clone();
        wupin_liebiao_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_lianjie)
    } else {
        wupin_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 清除物品列表缓存
    match liebiao_guanli.qingchu_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_wupin_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_wupin_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_qingchu_wupin_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/liebiao/huancun")]
pub fn qingchu_wupin_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 根据类名精确查询物品列表接口
#[get("/youxishuju/wupin/mingzi/leiming/<leiming>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_wupin_by_leiming_jingque(
    leiming: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_mingzi_leiming_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_mingzi_leiming_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_mingzi_leiming_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建物品名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_wupin_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::leiming_jingque(leiming.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_leiming_wupin(&leiming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_mingzi(&leiming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_leiming_wupin(&leiming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_leiming_wupin(&leiming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/mingzi/leiming/<_leiming>")]
pub fn chaxun_wupin_by_leiming_jingque_yujian(_leiming: String) -> Status {
    Status::Ok
}

/// 根据名字精确查询物品列表接口
#[get("/youxishuju/wupin/mingzi/jingque/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_wupin_by_mingzi_jingque(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_mingzi_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_mingzi_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_mingzi_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建物品名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_wupin_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_jingque(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_mingzi_wupin(&mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_mingzi(&mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_mingzi_wupin(&mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_mingzi_wupin(&mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/mingzi/jingque/<_mingzi>")]
pub fn chaxun_wupin_by_mingzi_jingque_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 根据名字模糊查询物品列表接口
#[get("/youxishuju/wupin/mingzi/mohu/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_wupin_by_mingzi_mohu(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_mingzi_mohu::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_mingzi_mohu::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_mingzi_mohu::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证查询字符串长度，模糊查询至少需要2个字符
    if mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::mohu_chaxun_zishu_buzu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建物品名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_wupin_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_mohu_chaxun_wupin(&mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_wupin_mingzi(&mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_mohu_chaxun_wupin(&mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_mohu_chaxun_wupin(&mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/mingzi/mohu/<_mingzi>")]
pub fn chaxun_wupin_by_mingzi_mohu_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 清除物品名字查询缓存接口
#[delete("/youxishuju/wupin/mingzi/huancun")]
pub async fn qingchu_wupin_mingzi_chaxun_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_wupin_mingzi_chaxun_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_wupin_mingzi_chaxun_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_wupin_mingzi_chaxun_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_wupin_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除缓存
    match mingzi_chaxun_guanli.qingchu_mingzi_chaxun_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_mingzi_chaxun_huancun(),
                serde_json::Value::Null,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_mingzi_chaxun_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_qingchu_mingzi_chaxun_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/mingzi/huancun")]
pub fn qingchu_wupin_mingzi_chaxun_huancun_yujian() -> Status {
    Status::Ok
}

/// 获取所有物品类型列表接口
#[get("/youxishuju/wupin/fenlei/leixing")]
pub async fn huoqu_wupin_fenlei_leixing(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_fenlei_leixing::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_fenlei_leixing::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_fenlei_leixing::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品分类输出管理器
    let fenlei_guanli = chuangjian_wupin_fenlei_shuchu_guanli(mysql_guanli, redis_guanli);

    // 获取所有类型列表
    match fenlei_guanli.huoqu_suoyou_leixing().await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let leixing_shuliang = jieguo.leixing_liebiao.as_ref().map(|list| list.len()).unwrap_or(0);
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_huoqu_fenlei_leixing(leixing_shuliang),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_huoqu_fenlei_leixing());
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_huoqu_fenlei_leixing())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_huoqu_fenlei_leixing(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/leixing")]
pub fn huoqu_wupin_fenlei_leixing_yujian() -> Status {
    Status::Ok
}

/// 根据类型获取子类型列表接口
#[get("/youxishuju/wupin/fenlei/zileixing/<leixing>")]
pub async fn huoqu_wupin_fenlei_zileixing(
    leixing: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_fenlei_zileixing::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_fenlei_zileixing::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_fenlei_zileixing::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品分类输出管理器
    let fenlei_guanli = chuangjian_wupin_fenlei_shuchu_guanli(mysql_guanli, redis_guanli);

    // 获取指定类型的子类型列表
    match fenlei_guanli.huoqu_zileixing_liebiao(&leixing).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let zileixing_shuliang = jieguo.zileixing_liebiao.as_ref().map(|list| list.len()).unwrap_or(0);
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_huoqu_fenlei_zileixing(&leixing, zileixing_shuliang),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_huoqu_fenlei_zileixing(&leixing));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_huoqu_fenlei_zileixing(&leixing))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_huoqu_fenlei_zileixing(&leixing, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/zileixing/<_leixing>")]
pub fn huoqu_wupin_fenlei_zileixing_yujian(_leixing: String) -> Status {
    Status::Ok
}

/// 获取完整分类信息接口
#[get("/youxishuju/wupin/fenlei/wanzheng")]
pub async fn huoqu_wupin_wanzheng_fenlei(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_wanzheng_fenlei::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_wanzheng_fenlei::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_wanzheng_fenlei::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品分类输出管理器
    let fenlei_guanli = chuangjian_wupin_fenlei_shuchu_guanli(mysql_guanli, redis_guanli);

    // 获取完整分类信息
    match fenlei_guanli.huoqu_wanzheng_fenlei().await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let fenlei_shuliang = jieguo.wanzheng_fenlei.as_ref().map(|list| list.len()).unwrap_or(0);
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_huoqu_wanzheng_fenlei(fenlei_shuliang),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_huoqu_wanzheng_fenlei());
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_huoqu_wanzheng_fenlei())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_huoqu_wanzheng_fenlei(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/wanzheng")]
pub fn huoqu_wupin_wanzheng_fenlei_yujian() -> Status {
    Status::Ok
}

/// 根据类型查询物品列表接口
#[get("/youxishuju/wupin/fenlei/leixing/<leixing>/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_wupin_by_leixing(
    leixing: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_leixing_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_leixing_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_leixing_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建物品分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_wupin_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match fenlei_liebiao_guanli.chaxun_by_leixing(&leixing, &fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_leixing_wupin_liebiao(&leixing, fenye_canshu.yema, jieguo.wupin_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_leixing_wupin_liebiao(&leixing, fenye_canshu.yema, fenye_canshu.meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_leixing_wupin_liebiao(&leixing))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_leixing_wupin_liebiao(&leixing, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/leixing/<_leixing>/liebiao")]
pub fn chaxun_wupin_by_leixing_yujian(_leixing: String) -> Status {
    Status::Ok
}

/// 根据类型和子类型查询物品列表接口
#[get("/youxishuju/wupin/fenlei/leixing/<leixing>/zileixing/<zileixing>/liebiao?<yema>&<meiye_shuliang>"
)]
pub async fn chaxun_wupin_by_leixing_zileixing(
    leixing: String,
    zileixing: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_leixing_zileixing_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_leixing_zileixing_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_leixing_zileixing_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建物品分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_wupin_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match fenlei_liebiao_guanli.chaxun_by_leixing_zileixing(&leixing, &zileixing, &fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_chaxun_leixing_zileixing_wupin_liebiao(&leixing, &zileixing, fenye_canshu.yema, jieguo.wupin_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_leixing_zileixing_wupin_liebiao(&leixing, &zileixing, fenye_canshu.yema, fenye_canshu.meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_chaxun_leixing_zileixing_wupin_liebiao(&leixing, &zileixing))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_chaxun_leixing_zileixing_wupin_liebiao(&leixing, &zileixing, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/leixing/<_leixing>/zileixing/<_zileixing>/liebiao")]
pub fn chaxun_wupin_by_leixing_zileixing_yujian(_leixing: String, _zileixing: String) -> Status {
    Status::Ok
}

/// 清除物品分类缓存接口
#[delete("/youxishuju/wupin/fenlei/huancun")]
pub async fn qingchu_wupin_fenlei_huancun(
    _mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_wupin_fenlei_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_wupin_fenlei_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_wupin_fenlei_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    if let Some(redis_state) = redis_guanli {
        let redis_kongzhi = wupin_redis_kongzhi::new(redis_state.as_ref().clone());

        match redis_kongzhi.qingchu_fenlei_huancun().await {
            Ok(_) => {
                let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_fenlei_huancun(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_fenlei_huancun());
                Json(xiangying)
            }
            Err(e) => {
                let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_qingchu_fenlei_huancun(&e.to_string())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
    } else {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::chenggong_xiangying(
            "Redis未启用，无需清除缓存".to_string(),
            None,
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
        Json(xiangying)
    }
}

#[options("/youxishuju/wupin/fenlei/huancun")]
pub fn qingchu_wupin_fenlei_huancun_yujian() -> Status {
    Status::Ok
}

/// 清除物品分类列表查询缓存接口
#[delete("/youxishuju/wupin/fenlei/liebiao/huancun")]
pub async fn qingchu_wupin_fenlei_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_wupin_fenlei_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_wupin_fenlei_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_wupin_fenlei_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建物品分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_wupin_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除缓存
    match fenlei_liebiao_guanli.qingchu_fenlei_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_fenlei_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_qingchu_fenlei_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_qingchu_fenlei_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/liebiao/huancun")]
pub fn qingchu_wupin_fenlei_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 根据类型和名字联合查询物品列表接口
#[get("/youxishuju/wupin/fenlei/leixing/<leixing>/mingzi/<mingzi_leixing>/<mingzi>?<yema>&<meiye_shuliang>"
)]
pub async fn lianhe_chaxun_wupin_by_leixing_mingzi(
    leixing: String,
    mingzi_leixing: String,
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_lianhe_chaxun_leixing_mingzi::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_lianhe_chaxun_leixing_mingzi::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_lianhe_chaxun_leixing_mingzi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证名字查询类型
    let mingzi_chaxun_leixing = match mingzi_leixing.as_str() {
        "jingque" => mingzi_chaxun_leixing::mingzi_jingque,
        "mohu" => mingzi_chaxun_leixing::mingzi_mohu,
        "leiming" => mingzi_chaxun_leixing::leiming_jingque,
        _ => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                "无效的名字查询类型，支持：jingque(精确)、mohu(模糊)、leiming(类名)".to_string()
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 模糊查询需要至少2个字符
    if mingzi_chaxun_leixing == mingzi_chaxun_leixing::mingzi_mohu && mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::mohu_chaxun_zishu_buzu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建名字查询条件
    let mingzi_tiaojian = mingzi_chaxun_tiaojian {
        chaxun_leixing: mingzi_chaxun_leixing,
        chaxun_zhi: mingzi.clone(),
        fenye_canshu: fenye_canshu.clone(),
    };

    // 创建物品分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_wupin_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行联合查询
    match fenlei_liebiao_guanli.chaxun_by_leixing_mingzi(&leixing, &mingzi_tiaojian, &fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_lianhe_chaxun_leixing_mingzi(&leixing, &mingzi, fenye_canshu.yema, jieguo.wupin_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_lianhe_chaxun_leixing_mingzi(&leixing, &mingzi, fenye_canshu.yema, fenye_canshu.meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_lianhe_chaxun_leixing_mingzi(&leixing, &mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_lianhe_chaxun_leixing_mingzi(&leixing, &mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/leixing/<_leixing>/mingzi/<_mingzi_leixing>/<_mingzi>")]
pub fn lianhe_chaxun_wupin_by_leixing_mingzi_yujian(_leixing: String, _mingzi_leixing: String, _mingzi: String) -> Status {
    Status::Ok
}

/// 根据类型、子类型和名字联合查询物品列表接口
#[get("/youxishuju/wupin/fenlei/leixing/<leixing>/zileixing/<zileixing>/mingzi/<mingzi_leixing>/<mingzi>?<yema>&<meiye_shuliang>"
)]
pub async fn lianhe_chaxun_wupin_by_leixing_zileixing_mingzi(
    leixing: String,
    zileixing: String,
    mingzi_leixing: String,
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi::get_miaoshu();
    let qingqiu_lujing = jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi::get_lujing();
    let qingqiu_fangfa = jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证名字查询类型
    let mingzi_chaxun_leixing = match mingzi_leixing.as_str() {
        "jingque" => mingzi_chaxun_leixing::mingzi_jingque,
        "mohu" => mingzi_chaxun_leixing::mingzi_mohu,
        "leiming" => mingzi_chaxun_leixing::leiming_jingque,
        _ => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                "无效的名字查询类型，支持：jingque(精确)、mohu(模糊)、leiming(类名)".to_string()
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 模糊查询需要至少2个字符
    if mingzi_chaxun_leixing == mingzi_chaxun_leixing::mingzi_mohu && mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::mohu_chaxun_zishu_buzu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建名字查询条件
    let mingzi_tiaojian = mingzi_chaxun_tiaojian {
        chaxun_leixing: mingzi_chaxun_leixing,
        chaxun_zhi: mingzi.clone(),
        fenye_canshu: fenye_canshu.clone(),
    };

    // 创建物品分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_wupin_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行联合查询
    match fenlei_liebiao_guanli.lianhe_chaxun(&leixing, &zileixing, &mingzi_tiaojian, &fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::chenggong_lianhe_chaxun_leixing_zileixing_mingzi(&leixing, &zileixing, &mingzi, fenye_canshu.yema, jieguo.wupin_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::luyou_rizhi_chenggong_lianhe_chaxun_leixing_zileixing_mingzi(&leixing, &zileixing, &mingzi, fenye_canshu.yema, fenye_canshu.meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::shibai_lianhe_chaxun_leixing_zileixing_mingzi(&leixing, &zileixing, &mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                super::jiekou_wupinshuju_rizhi::wupin_jiekou_rizhi_guanli::cuowu_lianhe_chaxun_leixing_zileixing_mingzi(&leixing, &zileixing, &mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/wupin/fenlei/leixing/<_leixing>/zileixing/<_zileixing>/mingzi/<_mingzi_leixing>/<_mingzi>"
)]
pub fn lianhe_chaxun_wupin_by_leixing_zileixing_mingzi_yujian(_leixing: String, _zileixing: String, _mingzi_leixing: String, _mingzi: String) -> Status {
    Status::Ok
}

// 使用宏定义查询全部信息接口
dingyii_jiekou!(
    jiekou_wupin_quanbu_xinxi,
    lujing: "/youxishuju/wupin/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询物品全部信息",
    jieshao: "根据物品ID查询物品的全部信息，包括基础信息和汇总信息",
    routes: [chaxun_wupin_quanbu_xinxi, chaxun_wupin_quanbu_xinxi_yujian]
);

// 使用宏定义查询字段信息接口
dingyii_jiekou!(
    jiekou_wupin_ziduan_xinxi,
    lujing: "/youxishuju/wupin/xinxi/{字段名字}/{id}",
    fangfa: "GET",
    miaoshu: "查询物品指定字段信息",
    jieshao: "根据字段名和物品ID查询物品的指定字段信息",
    routes: [chaxun_wupin_ziduan_xinxi, chaxun_wupin_ziduan_xinxi_yujian]
);

// 使用宏定义删除缓存接口
dingyii_jiekou!(
    jiekou_shanchu_wupin_huancun,
    lujing: "/youxishuju/wupin/huancun/{id}",
    fangfa: "DELETE",
    miaoshu: "删除物品缓存",
    jieshao: "删除指定物品ID的Redis缓存数据，不影响其他数据",
    routes: [shanchu_wupin_huancun, shanchu_wupin_huancun_yujian]
);

// 使用宏定义清理所有缓存接口
dingyii_jiekou!(
    jiekou_qingchu_suoyou_wupin_huancun,
    lujing: "/youxishuju/wupin/huancun",
    fangfa: "DELETE",
    miaoshu: "清理所有物品缓存",
    jieshao: "一键清理所有物品全部信息的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_suoyou_wupin_huancun, qingchu_suoyou_wupin_huancun_yujian]
);

// 使用宏定义物品列表查询接口
dingyii_jiekou!(
    jiekou_wupin_liebiao,
    lujing: "/youxishuju/wupin/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "查询物品列表",
    jieshao: "分页查询物品列表，支持Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_wupin_liebiao, chaxun_wupin_liebiao_yujian]
);

// 使用宏定义清除物品列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_wupin_liebiao_huancun,
    lujing: "/youxishuju/wupin/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除物品列表缓存",
    jieshao: "清除所有物品列表的Redis缓存，不影响物品详情缓存",
    routes: [qingchu_wupin_liebiao_huancun, qingchu_wupin_liebiao_huancun_yujian]
);

// 使用宏定义物品类名精确查询接口
dingyii_jiekou!(
    jiekou_wupin_mingzi_leiming_jingque,
    lujing: "/youxishuju/wupin/mingzi/leiming/{类名}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类名精确查询物品列表",
    jieshao: "根据物品类名精确查询物品列表，支持分页和Redis缓存",
    routes: [chaxun_wupin_by_leiming_jingque, chaxun_wupin_by_leiming_jingque_yujian]
);

// 使用宏定义物品名字精确查询接口
dingyii_jiekou!(
    jiekou_wupin_mingzi_jingque,
    lujing: "/youxishuju/wupin/mingzi/jingque/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字精确查询物品列表",
    jieshao: "根据物品名字精确查询物品列表，支持分页和Redis缓存",
    routes: [chaxun_wupin_by_mingzi_jingque, chaxun_wupin_by_mingzi_jingque_yujian]
);

// 使用宏定义物品名字模糊查询接口
dingyii_jiekou!(
    jiekou_wupin_mingzi_mohu,
    lujing: "/youxishuju/wupin/mingzi/mohu/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字模糊查询物品列表",
    jieshao: "根据物品名字模糊查询物品列表，支持分页和Redis缓存，至少需要2个字符",
    routes: [chaxun_wupin_by_mingzi_mohu, chaxun_wupin_by_mingzi_mohu_yujian]
);

// 使用宏定义清除物品名字查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_wupin_mingzi_chaxun_huancun,
    lujing: "/youxishuju/wupin/mingzi/huancun",
    fangfa: "DELETE",
    miaoshu: "清除物品名字查询缓存",
    jieshao: "清除所有物品名字查询的Redis缓存，包括类名查询、精确查询和模糊查询",
    routes: [qingchu_wupin_mingzi_chaxun_huancun, qingchu_wupin_mingzi_chaxun_huancun_yujian]
);

// 使用宏定义获取物品分类类型列表接口
dingyii_jiekou!(
    jiekou_wupin_fenlei_leixing,
    lujing: "/youxishuju/wupin/fenlei/leixing",
    fangfa: "GET",
    miaoshu: "获取所有物品类型列表",
    jieshao: "获取数据库中所有物品的类型列表，支持Redis缓存",
    routes: [huoqu_wupin_fenlei_leixing, huoqu_wupin_fenlei_leixing_yujian]
);

// 使用宏定义根据类型获取子类型列表接口
dingyii_jiekou!(
    jiekou_wupin_fenlei_zileixing,
    lujing: "/youxishuju/wupin/fenlei/zileixing/{类型}",
    fangfa: "GET",
    miaoshu: "根据类型获取子类型列表",
    jieshao: "根据指定的物品类型获取其对应的子类型列表，支持Redis缓存",
    routes: [huoqu_wupin_fenlei_zileixing, huoqu_wupin_fenlei_zileixing_yujian]
);

// 使用宏定义获取完整分类信息接口
dingyii_jiekou!(
    jiekou_wupin_wanzheng_fenlei,
    lujing: "/youxishuju/wupin/fenlei/wanzheng",
    fangfa: "GET",
    miaoshu: "获取完整分类信息",
    jieshao: "获取所有物品类型及其对应的子类型的完整分类信息，支持Redis缓存",
    routes: [huoqu_wupin_wanzheng_fenlei, huoqu_wupin_wanzheng_fenlei_yujian]
);

// 使用宏定义根据类型查询物品列表接口
dingyii_jiekou!(
    jiekou_wupin_leixing_liebiao,
    lujing: "/youxishuju/wupin/fenlei/leixing/{类型}/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类型查询物品列表",
    jieshao: "根据指定的物品类型查询物品列表，支持分页和Redis缓存",
    routes: [chaxun_wupin_by_leixing, chaxun_wupin_by_leixing_yujian]
);

// 使用宏定义根据类型和子类型查询物品列表接口
dingyii_jiekou!(
    jiekou_wupin_leixing_zileixing_liebiao,
    lujing: "/youxishuju/wupin/fenlei/leixing/{类型}/zileixing/{子类型}/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类型和子类型查询物品列表",
    jieshao: "根据指定的物品类型和子类型查询物品列表，支持分页和Redis缓存",
    routes: [chaxun_wupin_by_leixing_zileixing, chaxun_wupin_by_leixing_zileixing_yujian]
);

// 使用宏定义清除物品分类缓存接口
dingyii_jiekou!(
    jiekou_qingchu_wupin_fenlei_huancun,
    lujing: "/youxishuju/wupin/fenlei/huancun",
    fangfa: "DELETE",
    miaoshu: "清除物品分类缓存",
    jieshao: "清除所有物品分类相关的Redis缓存，包括类型列表、子类型列表和完整分类信息",
    routes: [qingchu_wupin_fenlei_huancun, qingchu_wupin_fenlei_huancun_yujian]
);

// 使用宏定义清除物品分类列表查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_wupin_fenlei_liebiao_huancun,
    lujing: "/youxishuju/wupin/fenlei/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除物品分类列表查询缓存",
    jieshao: "清除所有物品分类列表查询的Redis缓存，不影响分类信息缓存",
    routes: [qingchu_wupin_fenlei_liebiao_huancun, qingchu_wupin_fenlei_liebiao_huancun_yujian]
);

// 使用宏定义类型和名字联合查询物品列表接口
dingyii_jiekou!(
    jiekou_wupin_lianhe_chaxun_leixing_mingzi,
    lujing: "/youxishuju/wupin/fenlei/leixing/{类型}/mingzi/{名字类型}/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类型和名字联合查询物品列表",
    jieshao: "根据指定的物品类型和名字条件联合查询物品列表，支持精确、模糊、类名查询，支持分页和Redis缓存",
    routes: [lianhe_chaxun_wupin_by_leixing_mingzi, lianhe_chaxun_wupin_by_leixing_mingzi_yujian]
);

// 使用宏定义类型、子类型和名字联合查询物品列表接口
dingyii_jiekou!(
    jiekou_wupin_lianhe_chaxun_leixing_zileixing_mingzi,
    lujing: "/youxishuju/wupin/fenlei/leixing/{类型}/zileixing/{子类型}/mingzi/{名字类型}/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类型、子类型和名字联合查询物品列表",
    jieshao: "根据指定的物品类型、子类型和名字条件联合查询物品列表，支持精确、模糊、类名查询，支持分页和Redis缓存",
    routes: [lianhe_chaxun_wupin_by_leixing_zileixing_mingzi, lianhe_chaxun_wupin_by_leixing_zileixing_mingzi_yujian]
);