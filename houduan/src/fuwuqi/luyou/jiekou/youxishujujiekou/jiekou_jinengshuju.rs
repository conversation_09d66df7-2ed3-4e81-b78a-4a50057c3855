#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jineng_liebiao_shuju::jineng_liebiao_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jineng_mingzi_chaxun::{jineng_mingzi_chaxun_guanli, jineng_mingzi_chaxun_tiaojian};
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jineng_redis_kongzhi::jineng_redis_kongzhi;
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jineng_rizhi_kongzhi::jineng_zifuchuan_changliangguanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jinengshujujiegouti::{jineng_liebiao_fenye_canshu, jineng_ziduan_yingshe};
use crate::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jinengshujuchuli::jineng_shuju_guanli;
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::jiekou::youxishujujiekou::jiekou_jinengshuju_rizhi::jineng_jiekou_rizhi_guanli;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;

/// 辅助函数：创建技能数据管理器（带Redis缓存支持）
fn chuangjian_jineng_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> jineng_shuju_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        let redis_kongzhi = jineng_redis_kongzhi::new(redis_state.as_ref().clone());
        jineng_shuju_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_kongzhi)
    } else {
        // 没有Redis，创建不带缓存的管理器
        jineng_shuju_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建技能列表管理器（带Redis缓存支持）
fn chuangjian_jineng_liebiao_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> jineng_liebiao_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        let redis_kongzhi = jineng_redis_kongzhi::new(redis_state.as_ref().clone());
        jineng_liebiao_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_kongzhi)
    } else {
        // 没有Redis，创建不带缓存的管理器
        jineng_liebiao_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建技能名字查询管理器（带Redis缓存支持）
fn chuangjian_jineng_mingzi_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> jineng_mingzi_chaxun_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        jineng_mingzi_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        // 没有Redis，创建不带缓存的管理器
        jineng_mingzi_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 查询技能全部信息接口
#[get("/youxishuju/jineng/xinxi/<id>")]
pub async fn chaxun_jineng_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建技能数据管理器（带Redis缓存）
    let jineng_guanli = chuangjian_jineng_guanli(mysql_guanli, redis_guanli);

    // 查询技能全部信息
    match jineng_guanli.tongyong_chaxun(&id, jineng_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_quanbu_xinxi(&id),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_quanbu_xinxi(&id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng(&id))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/xinxi/<_id>")]
pub fn chaxun_jineng_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

/// 查询技能指定字段信息接口
#[get("/youxishuju/jineng/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_jineng_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证字段名是否有效
    if !jineng_ziduan_yingshe::jiancha_ziduan_youxiao(&ziduan_ming) {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::wuxiao_ziduan_ming(&ziduan_ming)
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建技能数据管理器（带Redis缓存）
    let jineng_guanli = chuangjian_jineng_guanli(mysql_guanli, redis_guanli);

    // 查询技能指定字段信息
    match jineng_guanli.tongyong_chaxun(&id, &ziduan_ming).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_ziduan_xinxi(&id, &ziduan_ming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_ziduan_xinxi(&id, &ziduan_ming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng_ziduan(&id, &ziduan_ming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng_ziduan(&id, &ziduan_ming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_jineng_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

/// 查询技能列表接口（分页）
#[get("/youxishuju/jineng/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_jineng_liebiao(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = jineng_liebiao_fenye_canshu {
        meiye_shuliang,
        dangqian_ye: yema,
    };

    // 创建技能列表管理器
    let liebiao_guanli = chuangjian_jineng_liebiao_guanli(mysql_guanli, redis_guanli);

    // 查询技能列表
    match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_liebiao(yema, jieguo.jineng_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_liebiao(yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng_liebiao())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/liebiao")]
pub fn chaxun_jineng_liebiao_yujian() -> Status {
    Status::Ok
}

/// 清除技能列表缓存接口
#[delete("/youxishuju/jineng/liebiao/huancun")]
pub async fn qingchu_jineng_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_jineng_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_jineng_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_jineng_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建技能列表管理器
    let liebiao_guanli = chuangjian_jineng_liebiao_guanli(mysql_guanli, redis_guanli);

    // 清除技能列表缓存
    match liebiao_guanli.qingchu_jineng_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                jineng_jiekou_rizhi_guanli::chenggong_qingchu_jineng_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_jineng_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_qingchu_jineng_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/liebiao/huancun")]
pub fn qingchu_jineng_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 根据类名精确查询技能列表接口
#[get("/youxishuju/jineng/leiming/<leiming>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_jineng_by_leiming_jingque(
    leiming: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_leiming_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_leiming_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_leiming_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = jineng_liebiao_fenye_canshu {
        meiye_shuliang,
        dangqian_ye: yema,
    };
    let chaxun_tiaojian = jineng_mingzi_chaxun_tiaojian::leiming_jingque(leiming.clone(), fenye_canshu);

    // 创建技能名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_jineng_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_mingzi("类名精确查询", &leiming, jieguo.jineng_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_mingzi("类名精确查询", &leiming, yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng_mingzi("类名精确查询", &leiming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng_mingzi("类名精确查询", &leiming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/leiming/<_leiming>")]
pub fn chaxun_jineng_by_leiming_jingque_yujian(_leiming: String) -> Status {
    Status::Ok
}

/// 根据名字精确查询技能列表接口
#[get("/youxishuju/jineng/mingzi/jingque/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_jineng_by_mingzi_jingque(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_mingzi_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_mingzi_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_mingzi_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = jineng_liebiao_fenye_canshu {
        meiye_shuliang,
        dangqian_ye: yema,
    };
    let chaxun_tiaojian = jineng_mingzi_chaxun_tiaojian::mingzi_jingque(mingzi.clone(), fenye_canshu);

    // 创建技能名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_jineng_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_mingzi("名字精确查询", &mingzi, jieguo.jineng_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_mingzi("名字精确查询", &mingzi, yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng_mingzi("名字精确查询", &mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng_mingzi("名字精确查询", &mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/mingzi/jingque/<_mingzi>")]
pub fn chaxun_jineng_by_mingzi_jingque_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 根据名字模糊查询技能列表接口
#[get("/youxishuju/jineng/mingzi/mohu/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_jineng_by_mingzi_mohu(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_jineng_mingzi_mohu::get_miaoshu();
    let qingqiu_lujing = jiekou_jineng_mingzi_mohu::get_lujing();
    let qingqiu_fangfa = jiekou_jineng_mingzi_mohu::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            jineng_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = jineng_liebiao_fenye_canshu {
        meiye_shuliang,
        dangqian_ye: yema,
    };
    let chaxun_tiaojian = jineng_mingzi_chaxun_tiaojian::mingzi_mohu(mingzi.clone(), fenye_canshu);

    // 创建技能名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_jineng_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    jineng_jiekou_rizhi_guanli::chenggong_chaxun_jineng_mingzi("名字模糊查询", &mingzi, jieguo.jineng_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_jineng_mingzi("名字模糊查询", &mingzi, yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| jineng_jiekou_rizhi_guanli::shibai_chaxun_jineng_mingzi("名字模糊查询", &mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_chaxun_jineng_mingzi("名字模糊查询", &mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/mingzi/mohu/<_mingzi>")]
pub fn chaxun_jineng_by_mingzi_mohu_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 清除技能名字查询缓存接口
#[delete("/youxishuju/jineng/mingzi/huancun")]
pub async fn qingchu_jineng_mingzi_chaxun_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_jineng_mingzi_chaxun_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_jineng_mingzi_chaxun_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_jineng_mingzi_chaxun_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建技能名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_jineng_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除技能名字查询缓存
    match mingzi_chaxun_guanli.qingchu_mingzi_chaxun_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                jineng_jiekou_rizhi_guanli::chenggong_qingchu_jineng_mingzi_chaxun_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &jineng_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_jineng_mingzi_chaxun_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                jineng_jiekou_rizhi_guanli::cuowu_qingchu_jineng_mingzi_chaxun_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/jineng/mingzi/huancun")]
pub fn qingchu_jineng_mingzi_chaxun_huancun_yujian() -> Status {
    Status::Ok
}

// 使用宏定义查询全部信息接口
dingyii_jiekou!(
    jiekou_jineng_quanbu_xinxi,
    lujing: "/youxishuju/jineng/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询技能全部信息",
    jieshao: "根据技能ID查询技能的全部信息，包括基础信息和汇总信息",
    routes: [chaxun_jineng_quanbu_xinxi, chaxun_jineng_quanbu_xinxi_yujian]
);

// 使用宏定义查询字段信息接口
dingyii_jiekou!(
    jiekou_jineng_ziduan_xinxi,
    lujing: "/youxishuju/jineng/xinxi/{字段名字}/{id}",
    fangfa: "GET",
    miaoshu: "查询技能指定字段信息",
    jieshao: "根据字段名和技能ID查询技能的指定字段信息",
    routes: [chaxun_jineng_ziduan_xinxi, chaxun_jineng_ziduan_xinxi_yujian]
);

// 使用宏定义技能列表查询接口
dingyii_jiekou!(
    jiekou_jineng_liebiao,
    lujing: "/youxishuju/jineng/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "查询技能列表",
    jieshao: "分页查询技能列表，支持Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_jineng_liebiao, chaxun_jineng_liebiao_yujian]
);

// 使用宏定义清除技能列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_jineng_liebiao_huancun,
    lujing: "/youxishuju/jineng/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除技能列表缓存",
    jieshao: "清除所有技能列表的Redis缓存，不影响技能详情缓存",
    routes: [qingchu_jineng_liebiao_huancun, qingchu_jineng_liebiao_huancun_yujian]
);

// 使用宏定义技能类名精确查询接口
dingyii_jiekou!(
    jiekou_jineng_leiming_jingque,
    lujing: "/youxishuju/jineng/leiming/{类名}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类名精确查询技能列表",
    jieshao: "根据技能类名精确查询技能列表，支持分页和Redis缓存",
    routes: [chaxun_jineng_by_leiming_jingque, chaxun_jineng_by_leiming_jingque_yujian]
);

// 使用宏定义技能名字精确查询接口
dingyii_jiekou!(
    jiekou_jineng_mingzi_jingque,
    lujing: "/youxishuju/jineng/mingzi/jingque/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字精确查询技能列表",
    jieshao: "根据技能名字精确查询技能列表，同时搜索skill_name和jineng_huizong表，支持分页和Redis缓存",
    routes: [chaxun_jineng_by_mingzi_jingque, chaxun_jineng_by_mingzi_jingque_yujian]
);

// 使用宏定义技能名字模糊查询接口
dingyii_jiekou!(
    jiekou_jineng_mingzi_mohu,
    lujing: "/youxishuju/jineng/mingzi/mohu/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字模糊查询技能列表",
    jieshao: "根据技能名字模糊查询技能列表，同时搜索skill_name和jineng_huizong表，支持分页和Redis缓存",
    routes: [chaxun_jineng_by_mingzi_mohu, chaxun_jineng_by_mingzi_mohu_yujian]
);

// 使用宏定义清除技能名字查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_jineng_mingzi_chaxun_huancun,
    lujing: "/youxishuju/jineng/mingzi/huancun",
    fangfa: "DELETE",
    miaoshu: "清除技能名字查询缓存",
    jieshao: "清除所有技能名字查询的Redis缓存，包括类名查询、名字精确查询和名字模糊查询",
    routes: [qingchu_jineng_mingzi_chaxun_huancun, qingchu_jineng_mingzi_chaxun_huancun_yujian]
);