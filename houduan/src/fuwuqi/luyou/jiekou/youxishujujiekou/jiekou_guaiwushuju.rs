#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_fenlei_liebiao_chaxun::guaiwu_fenlei_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_lianhe_sousuo::{guaiwu_lianhe_sousuo_canshu, guaiwu_lianhe_sousuo_guanli, mingzi_sousuo_leixing, mingzi_sousuo_tiaojian};
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_mingzi_chaxun::{guaiwu_mingzi_chaxun_guanli, mingzi_chaxun_tiaojian};
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwujiegouti::{guaiwu_fenlei_chaxun_canshu, guaiwu_ziduan_yingshe};
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwushuju_liebiao_chaxun::guaiwu_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwushujuchuli::guaiwu_shuju_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::jiekou::youxishujujiekou::jiekou_guaiwushuju_rizhi::guaiwu_jiekou_rizhi_guanli;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;

/// 通用管理器创建辅助函数
fn chuangjian_guanli_with_redis<T, F1, F2>(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
    with_redis_fn: F1,
    without_redis_fn: F2,
) -> T
where
    F1: FnOnce(mysql_lianjie_guanli, guaiwu_redis_kongzhi) -> T,
    F2: FnOnce(mysql_lianjie_guanli) -> T,
{
    if let Some(redis_state) = redis_guanli {
        let redis_kongzhi = guaiwu_redis_kongzhi::new(redis_state.as_ref().clone());
        with_redis_fn(mysql_guanli.as_ref().clone(), redis_kongzhi)
    } else {
        without_redis_fn(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建怪物数据管理器（带Redis缓存支持）
fn chuangjian_guaiwu_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_shuju_guanli {
    chuangjian_guanli_with_redis(
        mysql_guanli,
        redis_guanli,
        guaiwu_shuju_guanli::new_with_redis,
        guaiwu_shuju_guanli::new,
    )
}

/// 查询怪物全部信息接口
#[get("/youxishuju/guaiwu/xinxi/<id>")]
pub async fn chaxun_guaiwu_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 查询怪物全部信息
    match guaiwu_guanli.tongyong_chaxun(&id, guaiwu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_guaiwu_quanbu_xinxi(&id),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_guaiwu_quanbu_xinxi(&id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_guaiwu(&id))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_guaiwu(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/xinxi/<_id>")]
pub fn chaxun_guaiwu_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

/// 查询怪物指定字段信息接口
#[get("/youxishuju/guaiwu/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_guaiwu_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证字段名是否有效
    if !guaiwu_ziduan_yingshe::jiancha_ziduan_youxiao(&ziduan_ming) {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::wuxiao_ziduan_ming(&ziduan_ming)
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 查询怪物指定字段信息
    match guaiwu_guanli.tongyong_chaxun(&id, &ziduan_ming).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_guaiwu_ziduan_xinxi(&id, &ziduan_ming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_guaiwu_ziduan_xinxi(&id, &ziduan_ming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_guaiwu_ziduan(&id, &ziduan_ming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_guaiwu_ziduan(&id, &ziduan_ming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_guaiwu_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

/// 删除怪物缓存接口
#[delete("/youxishuju/guaiwu/huancun/<id>")]
pub async fn shanchu_guaiwu_huancun(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_shanchu_guaiwu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_shanchu_guaiwu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_shanchu_guaiwu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 删除怪物缓存
    match guaiwu_guanli.shanchu_guaiwu_huancun(&id).await {
        Ok(shanchu_chenggong) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_chenggong {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    guaiwu_jiekou_rizhi_guanli::chenggong_shanchu_guaiwu_huancun(&id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_shanchu_guaiwu_huancun(&id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    guaiwu_jiekou_rizhi_guanli::guaiwu_huancun_buxunzai_huo_redis_weiqiyong(&id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_shanchu_guaiwu_huancun(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/huancun/<_id>")]
pub fn shanchu_guaiwu_huancun_yujian(_id: String) -> Status {
    Status::Ok
}

/// 清理所有怪物缓存接口
#[delete("/youxishuju/guaiwu/huancun")]
pub async fn qingchu_suoyou_guaiwu_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_suoyou_guaiwu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_suoyou_guaiwu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_suoyou_guaiwu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 清理所有怪物缓存
    match guaiwu_guanli.qingchu_suoyou_guaiwu_huancun().await {
        Ok(shanchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_shuliang > 0 {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_guaiwu_huancun(shanchu_shuliang as u32),
                    serde_json::json!({"shanchu_shuliang": shanchu_shuliang}),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_suoyou_guaiwu_huancun(shanchu_shuliang as i64));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    guaiwu_jiekou_rizhi_guanli::meiyou_xuyao_qingchu_huancun(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_qingchu_suoyou_guaiwu_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/huancun")]
pub fn qingchu_suoyou_guaiwu_huancun_yujian() -> Status {
    Status::Ok
}

/// 查询怪物列表接口（分页）
#[get("/youxishuju/guaiwu/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_liebiao(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema, meiye_shuliang);

    // 创建怪物列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        guaiwu_liebiao_chaxun_guanli::new_with_redis(
            mysql_guanli.as_ref().clone(),
            redis_state.as_ref().clone(),
        )
    } else {
        guaiwu_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 查询怪物列表
    match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_guaiwu_liebiao(yema, jieguo.guaiwu_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_guaiwu_liebiao(yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_guaiwu_liebiao())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_guaiwu_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/liebiao")]
pub fn chaxun_guaiwu_liebiao_yujian() -> Status {
    Status::Ok
}

/// 清除怪物列表缓存接口
#[delete("/youxishuju/guaiwu/liebiao/huancun")]
pub async fn qingchu_guaiwu_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        guaiwu_liebiao_chaxun_guanli::new_with_redis(
            mysql_guanli.as_ref().clone(),
            redis_state.as_ref().clone(),
        )
    } else {
        guaiwu_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 清除怪物列表缓存
    match liebiao_guanli.qingchu_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                guaiwu_jiekou_rizhi_guanli::chenggong_qingchu_guaiwu_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_guaiwu_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_qingchu_guaiwu_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/liebiao/huancun")]
pub fn qingchu_guaiwu_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 怪物联合搜索接口
#[get("/youxishuju/guaiwu/lianhe_sousuo?<yuansu_fenlei>&<zhongzu_fenlei>&<biaozhi_fenlei>&<ai_fenlei>&<chicun_fenlei>&<mingzi>&<mingzi_leixing>&<yema>&<meiye_shuliang>")]
pub async fn guaiwu_lianhe_sousuo(
    yuansu_fenlei: Option<String>,
    zhongzu_fenlei: Option<String>,
    biaozhi_fenlei: Option<String>,
    ai_fenlei: Option<String>,
    chicun_fenlei: Option<String>,
    mingzi: Option<String>,
    mingzi_leixing: Option<String>,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_lianhe_sousuo::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_lianhe_sousuo::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_lianhe_sousuo::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 构建名字搜索条件
    let mingzi_sousuo = if let Some(mingzi_zhi) = mingzi {
        let sousuo_leixing = match mingzi_leixing.as_deref() {
            Some("jingque") => mingzi_sousuo_leixing::jingque,
            _ => mingzi_sousuo_leixing::mohu, // 默认模糊搜索
        };
        Some(mingzi_sousuo_tiaojian {
            mingzi: mingzi_zhi,
            sousuo_leixing,
        })
    } else {
        None
    };

    // 构建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 构建搜索参数
    let sousuo_canshu = guaiwu_lianhe_sousuo_canshu {
        yuansu_fenlei,
        zhongzu_fenlei,
        biaozhi_fenlei,
        ai_fenlei,
        chicun_fenlei,
        mingzi_sousuo,
        fenye_canshu,
    };

    // 创建联合搜索管理器
    let lianhe_sousuo_guanli = chuangjian_guaiwu_lianhe_sousuo_guanli(mysql_guanli, redis_guanli);

    // 执行联合搜索
    match lianhe_sousuo_guanli.lianhe_sousuo_guaiwu(&sousuo_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_lianhe_sousuo(jieguo.guaiwu_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_lianhe_sousuo(jieguo.guaiwu_liebiao.len()));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_lianhe_sousuo())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_lianhe_sousuo(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/lianhe_sousuo")]
pub fn guaiwu_lianhe_sousuo_yujian() -> Status {
    Status::Ok
}

/// 清除怪物联合搜索缓存接口
#[delete("/youxishuju/guaiwu/lianhe_sousuo/huancun")]
pub async fn qingchu_guaiwu_lianhe_sousuo_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_lianhe_sousuo_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_lianhe_sousuo_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_lianhe_sousuo_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建联合搜索管理器
    let lianhe_sousuo_guanli = chuangjian_guaiwu_lianhe_sousuo_guanli(mysql_guanli, redis_guanli);

    // 清除联合搜索缓存
    match lianhe_sousuo_guanli.qingchu_lianhe_sousuo_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                guaiwu_jiekou_rizhi_guanli::chenggong_qingchu_lianhe_sousuo_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_lianhe_sousuo_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_qingchu_lianhe_sousuo_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/lianhe_sousuo/huancun")]
pub fn qingchu_guaiwu_lianhe_sousuo_huancun_yujian() -> Status {
    Status::Ok
}

// 使用宏定义查询全部信息接口
dingyii_jiekou!(
    jiekou_guaiwu_quanbu_xinxi,
    lujing: "/youxishuju/guaiwu/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询怪物全部信息",
    jieshao: "根据怪物ID查询怪物的全部信息，包括基础信息和汇总信息",
    routes: [chaxun_guaiwu_quanbu_xinxi, chaxun_guaiwu_quanbu_xinxi_yujian]
);

// 使用宏定义查询字段信息接口
dingyii_jiekou!(
    jiekou_guaiwu_ziduan_xinxi,
    lujing: "/youxishuju/guaiwu/xinxi/{字段名字}/{id}",
    fangfa: "GET",
    miaoshu: "查询怪物指定字段信息",
    jieshao: "根据字段名和怪物ID查询怪物的指定字段信息",
    routes: [chaxun_guaiwu_ziduan_xinxi, chaxun_guaiwu_ziduan_xinxi_yujian]
);

// 使用宏定义删除缓存接口
dingyii_jiekou!(
    jiekou_shanchu_guaiwu_huancun,
    lujing: "/youxishuju/guaiwu/huancun/{id}",
    fangfa: "DELETE",
    miaoshu: "删除怪物缓存",
    jieshao: "删除指定怪物ID的Redis缓存数据，不影响其他数据",
    routes: [shanchu_guaiwu_huancun, shanchu_guaiwu_huancun_yujian]
);

// 使用宏定义清理所有缓存接口
dingyii_jiekou!(
    jiekou_qingchu_suoyou_guaiwu_huancun,
    lujing: "/youxishuju/guaiwu/huancun",
    fangfa: "DELETE",
    miaoshu: "清理所有怪物缓存",
    jieshao: "一键清理所有怪物全部信息的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_suoyou_guaiwu_huancun, qingchu_suoyou_guaiwu_huancun_yujian]
);

// 使用宏定义怪物列表查询接口
dingyii_jiekou!(
    jiekou_guaiwu_liebiao,
    lujing: "/youxishuju/guaiwu/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "查询怪物列表",
    jieshao: "分页查询怪物列表，支持Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_liebiao, chaxun_guaiwu_liebiao_yujian]
);

// 使用宏定义清除怪物列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_liebiao_huancun,
    lujing: "/youxishuju/guaiwu/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物列表缓存",
    jieshao: "清除所有怪物列表的Redis缓存，不影响怪物详情缓存",
    routes: [qingchu_guaiwu_liebiao_huancun, qingchu_guaiwu_liebiao_huancun_yujian]
);

/// 通用管理器创建辅助函数（用于直接接受redis_lianjie_guanli的管理器）
fn chuangjian_guanli_with_redis_direct<T, F1, F2>(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
    with_redis_fn: F1,
    without_redis_fn: F2,
) -> T
where
    F1: FnOnce(mysql_lianjie_guanli, redis_lianjie_guanli) -> T,
    F2: FnOnce(mysql_lianjie_guanli) -> T,
{
    if let Some(redis_state) = redis_guanli {
        with_redis_fn(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        without_redis_fn(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建怪物名字查询管理器（带Redis缓存支持）
fn chuangjian_guaiwu_mingzi_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_mingzi_chaxun_guanli {
    chuangjian_guanli_with_redis_direct(
        mysql_guanli,
        redis_guanli,
        guaiwu_mingzi_chaxun_guanli::new_with_redis,
        guaiwu_mingzi_chaxun_guanli::new,
    )
}

/// 辅助函数：创建怪物分类列表查询管理器（带Redis缓存支持）
fn chuangjian_guaiwu_fenlei_liebiao_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_fenlei_liebiao_chaxun_guanli {
    chuangjian_guanli_with_redis_direct(
        mysql_guanli,
        redis_guanli,
        guaiwu_fenlei_liebiao_chaxun_guanli::new_with_redis,
        guaiwu_fenlei_liebiao_chaxun_guanli::new,
    )
}

/// 辅助函数：创建怪物联合搜索管理器（带Redis缓存支持）
fn chuangjian_guaiwu_lianhe_sousuo_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_lianhe_sousuo_guanli {
    chuangjian_guanli_with_redis(
        mysql_guanli,
        redis_guanli,
        guaiwu_lianhe_sousuo_guanli::new_with_redis,
        guaiwu_lianhe_sousuo_guanli::new,
    )
}

/// 根据类名精确查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/leiming/<leiming>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_leiming_jingque(
    leiming: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_leiming_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_leiming_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_leiming_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::leiming_jingque(leiming.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_leiming_guaiwu(&leiming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_leiming_guaiwu(&leiming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_leiming_guaiwu(&leiming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_leiming_guaiwu(&leiming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/leiming/<_leiming>")]
pub fn chaxun_guaiwu_by_leiming_jingque_yujian(_leiming: String) -> Status {
    Status::Ok
}

/// 根据名字精确查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/jingque/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_mingzi_jingque(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_jingque(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_mingzi_guaiwu(&mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_mingzi_guaiwu(&mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_mingzi_guaiwu(&mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_mingzi_guaiwu(&mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/jingque/<_mingzi>")]
pub fn chaxun_guaiwu_by_mingzi_jingque_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 根据名字模糊查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/mohu/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_mingzi_mohu(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_mohu::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_mohu::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_mohu::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证查询字符串长度，模糊查询至少需要2个字符
    if mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::mohu_chaxun_zishu_buzu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_mohu_chaxun_guaiwu(&mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_mohu_chaxun_guaiwu(&mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_mohu_chaxun_guaiwu(&mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_mohu_chaxun_guaiwu(&mingzi, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/mohu/<_mingzi>")]
pub fn chaxun_guaiwu_by_mingzi_mohu_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 清除怪物名字查询缓存接口
#[delete("/youxishuju/guaiwu/mingzi/huancun")]
pub async fn qingchu_guaiwu_mingzi_chaxun_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除缓存
    match mingzi_chaxun_guanli.qingchu_mingzi_chaxun_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(guaiwu_jiekou_rizhi_guanli::chenggong_qingchu_mingzi_chaxun_huancun(), None);
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_mingzi_chaxun_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_qingchu_mingzi_chaxun_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/huancun")]
pub fn qingchu_guaiwu_mingzi_chaxun_huancun_yujian() -> Status {
    Status::Ok
}

/// 根据分类查询怪物列表接口
#[get("/youxishuju/guaiwu/fenlei/<fenlei_leixing>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_fenlei(
    fenlei_leixing: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_fenlei_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_fenlei_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_fenlei_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            guaiwu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分类查询参数
    let chaxun_canshu = guaiwu_fenlei_chaxun_canshu::new(fenlei_leixing.clone(), yema, meiye_shuliang);

    // 创建怪物分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_guaiwu_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 查询分类怪物列表
    match fenlei_liebiao_guanli.huoqu_fenlei_guaiwu_liebiao(&chaxun_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    guaiwu_jiekou_rizhi_guanli::chenggong_chaxun_fenlei_guaiwu(&fenlei_leixing, yema, jieguo.guaiwu_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_fenlei_guaiwu(&fenlei_leixing, yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| guaiwu_jiekou_rizhi_guanli::shibai_chaxun_fenlei_guaiwu(&fenlei_leixing))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_chaxun_fenlei_guaiwu(&fenlei_leixing, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/fenlei/<_fenlei_leixing>")]
pub fn chaxun_guaiwu_by_fenlei_yujian(_fenlei_leixing: String) -> Status {
    Status::Ok
}

/// 清除怪物分类列表缓存接口
#[delete("/youxishuju/guaiwu/fenlei/huancun")]
pub async fn qingchu_guaiwu_fenlei_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_fenlei_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_fenlei_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_fenlei_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物分类列表查询管理器
    let fenlei_liebiao_guanli = chuangjian_guaiwu_fenlei_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除分类列表缓存
    match fenlei_liebiao_guanli.qingchu_fenlei_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                guaiwu_jiekou_rizhi_guanli::chenggong_qingchu_fenlei_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_fenlei_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                guaiwu_jiekou_rizhi_guanli::cuowu_qingchu_fenlei_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/fenlei/huancun")]
pub fn qingchu_guaiwu_fenlei_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 获取支持的分类类型列表接口
#[get("/youxishuju/guaiwu/fenlei/leixing")]
pub async fn huoqu_zhichi_fenlei_leixing_liebiao() -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_fenlei_leixing_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_fenlei_leixing_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_fenlei_leixing_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 获取支持的分类类型列表
    let fenlei_leixing_liebiao = guaiwu_fenlei_liebiao_chaxun_guanli::huoqu_zhichi_fenlei_leixing_liebiao();

    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    let xiangying = mingwen_xiangying::chenggong_with_shuju(
        guaiwu_jiekou_rizhi_guanli::chenggong_huoqu_fenlei_leixing(fenlei_leixing_liebiao.len()),
        serde_json::json!({
            "fenlei_leixing_liebiao": fenlei_leixing_liebiao,
            "fenlei_shuliang": fenlei_leixing_liebiao.len()
        }),
    );
    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
    luyou_rizhi_xinxi(jiekou_ming, &guaiwu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_huoqu_fenlei_leixing());
    Json(xiangying)
}

#[options("/youxishuju/guaiwu/fenlei/leixing")]
pub fn huoqu_zhichi_fenlei_leixing_liebiao_yujian() -> Status {
    Status::Ok
}

// 使用宏定义怪物类名精确查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_leiming_jingque,
    lujing: "/youxishuju/guaiwu/mingzi/leiming/{类名}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类名精确查询怪物列表",
    jieshao: "根据怪物类名精确查询怪物列表，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_leiming_jingque, chaxun_guaiwu_by_leiming_jingque_yujian]
);

// 使用宏定义怪物名字精确查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_jingque,
    lujing: "/youxishuju/guaiwu/mingzi/jingque/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字精确查询怪物列表",
    jieshao: "根据怪物名字精确查询怪物列表，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_mingzi_jingque, chaxun_guaiwu_by_mingzi_jingque_yujian]
);

// 使用宏定义怪物名字模糊查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_mohu,
    lujing: "/youxishuju/guaiwu/mingzi/mohu/{名字关键词}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字模糊查询怪物列表",
    jieshao: "根据怪物名字关键词模糊查询怪物列表，支持分页和Redis缓存。关键词至少需要2个字符，页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_mingzi_mohu, chaxun_guaiwu_by_mingzi_mohu_yujian]
);

// 使用宏定义清除怪物名字查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_mingzi_chaxun_huancun,
    lujing: "/youxishuju/guaiwu/mingzi/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物名字查询缓存",
    jieshao: "清除所有怪物名字查询的Redis缓存，包括类名查询、精确名字查询和模糊名字查询的缓存",
    routes: [qingchu_guaiwu_mingzi_chaxun_huancun, qingchu_guaiwu_mingzi_chaxun_huancun_yujian]
);

// 使用宏定义怪物分类列表查询接口
dingyii_jiekou!(
    jiekou_guaiwu_fenlei_liebiao,
    lujing: "/youxishuju/guaiwu/fenlei/{分类类型}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据分类查询怪物列表",
    jieshao: "根据分类类型查询怪物列表，支持分页和Redis缓存。分类类型如：yuansu_huo(火元素)、zhongzu_human(人类)、biaozhi_boss(BOSS)等。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_fenlei, chaxun_guaiwu_by_fenlei_yujian]
);

// 使用宏定义清除怪物分类列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_fenlei_liebiao_huancun,
    lujing: "/youxishuju/guaiwu/fenlei/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物分类列表缓存",
    jieshao: "清除所有怪物分类列表的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_guaiwu_fenlei_liebiao_huancun, qingchu_guaiwu_fenlei_liebiao_huancun_yujian]
);

// 使用宏定义获取支持的分类类型列表接口
dingyii_jiekou!(
    jiekou_guaiwu_fenlei_leixing_liebiao,
    lujing: "/youxishuju/guaiwu/fenlei/leixing",
    fangfa: "GET",
    miaoshu: "获取支持的分类类型列表",
    jieshao: "获取所有支持的怪物分类类型列表，包括元素、种族、标志、AI、体型等五大分类的所有子类型",
    routes: [huoqu_zhichi_fenlei_leixing_liebiao, huoqu_zhichi_fenlei_leixing_liebiao_yujian]
);

// 使用宏定义怪物联合搜索接口
dingyii_jiekou!(
    jiekou_guaiwu_lianhe_sousuo,
    lujing: "/youxishuju/guaiwu/lianhe_sousuo?yuansu_fenlei={元素分类}&zhongzu_fenlei={种族分类}&biaozhi_fenlei={标志分类}&ai_fenlei={AI分类}&chicun_fenlei={体型分类}&mingzi={名字}&mingzi_leixing={名字搜索类型:jingque|mohu}&yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "怪物联合搜索",
    jieshao: "支持多条件联合搜索怪物，可组合元素、种族、标志、AI、体型分类和名字搜索（精确/模糊），支持分页和Redis缓存。所有参数都是可选的",
    routes: [guaiwu_lianhe_sousuo, guaiwu_lianhe_sousuo_yujian]
);

// 使用宏定义清除怪物联合搜索缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_lianhe_sousuo_huancun,
    lujing: "/youxishuju/guaiwu/lianhe_sousuo/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物联合搜索缓存",
    jieshao: "清除所有怪物联合搜索的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_guaiwu_lianhe_sousuo_huancun, qingchu_guaiwu_lianhe_sousuo_huancun_yujian]
);