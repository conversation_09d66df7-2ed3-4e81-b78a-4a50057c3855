#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 地图接口日志管理器
pub struct ditu_jiekou_rizhi_guanli;

impl ditu_jiekou_rizhi_guanli {
    // 成功信息
    pub fn chenggong_chaxun_ditu_quanbu_xinxi(id: &str) -> String {
        format!("成功查询到地图ID[{}]的全部信息", id)
    }

    pub fn chenggong_chaxun_ditu_ziduan_xinxi(id: &str, ziduan: &str) -> String {
        format!("成功查询到地图ID[{}]的字段[{}]信息", id, ziduan)
    }

    pub fn chenggong_shanchu_ditu_huancun(id: &str) -> String {
        format!("成功删除地图ID[{}]的缓存数据", id)
    }

    pub fn chenggong_qingchu_suoyou_ditu_huancun(shuliang: u32) -> String {
        format!("成功清理所有地图缓存，删除了{}个缓存项", shuliang)
    }

    // 失败信息
    pub fn shibai_chaxun_ditu(id: &str) -> String {
        format!("查询地图ID[{}]失败", id)
    }

    pub fn shibai_chaxun_ditu_ziduan(id: &str, ziduan: &str) -> String {
        format!("查询地图ID[{}]字段[{}]失败", id, ziduan)
    }

    // 错误信息
    pub fn cuowu_shanchu_ditu_huancun(id: &str, cuowu: &str) -> String {
        format!("删除地图ID[{}]缓存时发生错误: {}", id, cuowu)
    }

    pub fn cuowu_qingchu_suoyou_ditu_huancun(cuowu: &str) -> String {
        format!("清理所有地图缓存时发生错误: {}", cuowu)
    }

    pub fn cuowu_chaxun_ditu_quanbu_xinxi(id: &str, err: &str) -> String {
        format!("查询地图ID[{}]的全部信息时发生错误: {}", id, err)
    }

    pub fn cuowu_chaxun_ditu_ziduan_xinxi(id: &str, ziduan: &str, err: &str) -> String {
        format!("查询地图ID[{}]的字段[{}]信息时发生错误: {}", id, ziduan, err)
    }

    // 其他信息
    pub fn wuxiao_ziduan_ming(ziduan_ming: &str) -> String {
        format!("无效的字段名: {}", ziduan_ming)
    }

    pub fn ditu_huancun_buxunzai_huo_redis_weiqiyong(id: &str) -> String {
        format!("地图ID[{}]的缓存不存在或Redis未启用", id)
    }

    pub fn meiyou_xuyao_qingchu_de_huancun() -> String {
        "没有找到需要清理的地图缓存或Redis未启用".to_string()
    }
}