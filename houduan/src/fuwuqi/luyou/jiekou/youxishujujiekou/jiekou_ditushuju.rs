#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditushujuchuli::ditushujuchuli;
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::jiekou::youxishujujiekou::jiekou_ditushuju_rizhi::ditu_jiekou_rizhi_guanli;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;

fn chuangjian_ditu_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditushujuchuli {
    if let Some(redis_state) = redis_guanli {
        ditushujuchuli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditushujuchuli::new(mysql_guanli.as_ref().clone())
    }
}

#[get("/youxishuju/ditu/xinxi/<id>")]
pub async fn chaxun_ditu_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.huoqu_ditu_quanbu_xinxi(&id).await {
        Ok(Some(jieguo)) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_quanbu_xinxi(&id),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_quanbu_xinxi(&id));
            Json(xiangying)
        }
        Ok(None) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::shibai_chaxun_ditu(&id)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_quanbu_xinxi(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/xinxi/<_id>")]
pub fn chaxun_ditu_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

#[get("/youxishuju/ditu/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_ditu_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // if !ditu_sql_kongzhi::jiancha_ziduan_hefa(&ziduan_ming) {
    //     let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    //     let xiangying = mingwen_xiangying::shibai_xiangying(
    //         ditu_jiekou_rizhi_guanli::wuxiao_ziduan_ming(&ziduan_ming)
    //     );
    //     luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
    //     return Json(xiangying);
    // }

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.huoqu_ditu_quanbu_xinxi(&id).await {
        Ok(Some(jieguo)) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming));
            Json(xiangying)
        }
        Ok(None) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::shibai_chaxun_ditu(&id)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_ditu_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

#[delete("/youxishuju/ditu/huancun")]
pub async fn qingchu_suoyou_ditu_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_suoyou_ditu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_suoyou_ditu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_suoyou_ditu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.qingchu_suoyou_ditu_huancun().await {
        Ok(shanchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            if shanchu_shuliang > 0 {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    ditu_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_ditu_huancun(shanchu_shuliang as u32),
                    serde_json::json!({"shanchu_shuliang": shanchu_shuliang}),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_ditu_huancun(shanchu_shuliang as u32));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    ditu_jiekou_rizhi_guanli::meiyou_xuyao_qingchu_de_huancun(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_qingchu_suoyou_ditu_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/huancun")]
pub fn qingchu_suoyou_ditu_huancun_yujian() -> Status {
    Status::Ok
}

dingyii_jiekou!(
    jiekou_ditu_quanbu_xinxi,
    lujing: "/youxishuju/ditu/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询地图全部信息",
    jieshao: "根据地图ID查询地图的全部信息",
    routes: [chaxun_ditu_quanbu_xinxi, chaxun_ditu_quanbu_xinxi_yujian]
);

dingyii_jiekou!(
    jiekou_ditu_ziduan_xinxi,
    lujing: "/youxishuju/ditu/xinxi/{字段名}/{id}",
    fangfa: "GET",
    miaoshu: "查询地图指定字段信息",
    jieshao: "根据字段名和地图ID查询地图的指定字段信息",
    routes: [chaxun_ditu_ziduan_xinxi, chaxun_ditu_ziduan_xinxi_yujian]
);

dingyii_jiekou!(
    jiekou_qingchu_suoyou_ditu_huancun,
    lujing: "/youxishuju/ditu/huancun",
    fangfa: "DELETE",
    miaoshu: "清理所有地图缓存",
    jieshao: "一键清理所有地图的Redis缓存",
    routes: [qingchu_suoyou_ditu_huancun, qingchu_suoyou_ditu_huancun_yujian]
);

pub fn get_routes() -> Vec<rocket::Route> {
    let mut routes = Vec::new();
    routes.extend(jiekou_ditu_quanbu_xinxi::get_routes());
    routes.extend(jiekou_ditu_ziduan_xinxi::get_routes());
    routes.extend(jiekou_qingchu_suoyou_ditu_huancun::get_routes());
    routes
}

pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        jiekou_ditu_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_ditu_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_qingchu_suoyou_ditu_huancun::get_jiekou_xinxi(),
    ]
}