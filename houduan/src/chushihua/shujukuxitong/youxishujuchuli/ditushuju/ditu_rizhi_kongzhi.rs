#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::{shujukuxitong_rizhi_cuowu, shujukuxitong_rizhi_xinxi};

/// 地图数据处理日志控制类
pub struct ditu_rizhi_kongzhi;

impl ditu_rizhi_kongzhi {
    /// 模块名称常量
    const mokuai_ming: &'static str = "地图数据库操作";

    // ==================== 常量定义 ====================

    /// 数据来源：redis
    pub const shuju_laiyuan_huancun: &'static str = "redis";

    /// 数据来源：mysql
    pub const shuju_laiyuan_shujuku: &'static str = "mysql";

    /// 成功清除地图缓存消息
    pub const chenggong_qingchu_ditu_huancun_xiaoxi: &'static str = "成功清除地图缓存";

    /// 地图缓存不存在或已被清除消息
    pub const ditu_huancun_bu_cunzai_xiaoxi: &'static str = "地图缓存不存在或已被清除";

    /// Redis连接未配置消息
    pub const redis_lianjie_wei_peizhi_xiaoxi: &'static str = "Redis连接未配置";

    /// Redis连接未配置无法清除缓存消息
    pub const redis_wei_peizhi_wufa_qingchu_xiaoxi: &'static str = "Redis连接未配置，无法清除缓存";

    /// 字段名不合法或不存在错误消息前缀
    pub const ziduan_ming_bu_hefa_cuowu_qianzhui: &'static str = "字段名不合法或不存在";

    /// 字段名包含不安全字符错误消息前缀
    pub const ziduan_ming_bu_anquan_cuowu_qianzhui: &'static str = "字段名包含不安全字符";

    /// 清除地图缓存失败错误消息前缀
    pub const qingchu_ditu_huancun_shibai_qianzhui: &'static str = "清除地图缓存失败";

    /// name表ID字段名
    pub const name_biao_id_ziduan: &'static str = "name_table_id";

    /// name表字段前缀
    pub const name_biao_ziduan_qianzhui: &'static str = "name_table_";

    /// id字段名
    pub const id_ziduan_ming: &'static str = "id";

    /// Redis地图缓存键前缀
    pub const redis_ditu_jian_qianzhui: &'static str = "ditu_quanbu:";

    /// Redis地图缓存模式匹配
    pub const redis_ditu_moshi_pipei: &'static str = "ditu_quanbu:*";

    /// 地图全部信息缓存统计格式
    pub const ditu_quanbu_xinxi_huancun_tongji_geshi: &'static str = "地图全部信息缓存: {} 个";

    /// 获取地图缓存统计失败格式
    pub const huoqu_ditu_huancun_tongji_shibai_geshi: &'static str = "获取地图缓存统计失败: {}";

    // ==================== 成功日志方法 ====================

    /// 获取地图单个字段成功
    pub fn huoqu_ditu_ziduan_chenggong(ditu_id: &str, ziduan_ming: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("获取地图字段成功，地图ID: {}，字段: {}", ditu_id, ziduan_ming));
    }

    /// 获取地图全部信息成功
    pub fn huoqu_ditu_quanbu_xinxi_chenggong(ditu_id: &str, laiyuan: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("获取地图全部信息成功，地图ID: {}，数据来源: {}", ditu_id, laiyuan));
    }

    /// 缓存地图数据成功
    pub fn huancun_ditu_shuju_chenggong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("缓存地图数据成功，地图ID: {}", ditu_id));
    }

    /// 清除地图缓存成功
    pub fn qingchu_ditu_huancun_chenggong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("清除地图缓存成功，地图ID: {}", ditu_id));
    }

    /// Redis缓存命中
    pub fn redis_huancun_minzhong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis缓存命中，地图ID: {}", ditu_id));
    }

    /// Redis缓存未命中
    pub fn redis_huancun_wei_minzhong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis缓存未命中，地图ID: {}", ditu_id));
    }

    /// Redis设置缓存成功
    pub fn redis_shezhi_huancun_chenggong(ditu_id: &str, guoqi_shijian: i64) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis设置缓存成功，地图ID: {}，过期时间: {}秒", ditu_id, guoqi_shijian));
    }

    /// Redis删除缓存成功
    pub fn redis_shanchu_huancun_chenggong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis删除缓存成功，地图ID: {}", ditu_id));
    }

    /// Redis检查缓存存在
    pub fn redis_jiancha_huancun_cunzai(ditu_id: &str, cunzai: bool) {
        let zhuangtai = if cunzai { "存在" } else { "不存在" };
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis检查缓存{}，地图ID: {}", zhuangtai, ditu_id));
    }

    /// Redis批量清除缓存成功
    pub fn redis_piliang_qingchu_chenggong(shuliang: u64) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis批量清除缓存成功，清除数量: {}", shuliang));
    }

    /// Redis获取统计信息成功
    pub fn redis_huoqu_tongji_chenggong(tongji_xinxi: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("Redis获取统计信息成功: {}", tongji_xinxi));
    }

    // ==================== 错误日志方法 ====================

    /// 获取地图单个字段失败
    pub fn huoqu_ditu_ziduan_shibai(ditu_id: &str, ziduan_ming: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取地图字段失败，地图ID: {}，字段: {}，错误: {}", ditu_id, ziduan_ming, cuowu));
    }

    /// 获取地图全部信息失败
    pub fn huoqu_ditu_quanbu_xinxi_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取地图全部信息失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 字段名验证失败
    pub fn ziduan_ming_yanzheng_shibai(ziduan_ming: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("字段名验证失败，字段名: {}，原因: 字段名不合法或不存在", ziduan_ming));
    }

    /// 字段名不合法错误
    pub fn ziduan_ming_bu_hefa_cuowu(ziduan_ming: &str) -> String {
        format!("{}: {}", Self::ziduan_ming_bu_hefa_cuowu_qianzhui, ziduan_ming)
    }

    /// 字段名包含不安全字符错误
    pub fn ziduan_ming_bu_anquan_cuowu(ziduan_ming: &str) -> String {
        format!("{}: {}", Self::ziduan_ming_bu_anquan_cuowu_qianzhui, ziduan_ming)
    }

    /// 清除地图缓存失败错误消息
    pub fn qingchu_ditu_huancun_shibai_xiaoxi(cuowu: &str) -> String {
        format!("{}: {}", Self::qingchu_ditu_huancun_shibai_qianzhui, cuowu)
    }

    /// 缓存地图数据失败
    pub fn huancun_ditu_shuju_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("缓存地图数据失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 清除地图缓存失败
    pub fn qingchu_ditu_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("清除地图缓存失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 地图数据不存在
    pub fn ditu_shuju_bu_cunzai(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("地图数据不存在，地图ID: {}", ditu_id));
    }

    /// Redis连接失败
    pub fn redis_lianjie_shibai(caozuo: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis操作失败，操作: {}，错误: {}", caozuo, cuowu));
    }

    /// Redis设置缓存失败
    pub fn redis_shezhi_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis设置缓存失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// Redis获取缓存失败
    pub fn redis_huoqu_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis获取缓存失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// Redis删除缓存失败
    pub fn redis_shanchu_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis删除缓存失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// Redis检查缓存存在失败
    pub fn redis_jiancha_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis检查缓存存在失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// Redis批量清除缓存失败
    pub fn redis_piliang_qingchu_shibai(cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis批量清除缓存失败，错误: {}", cuowu));
    }

    /// Redis获取统计信息失败
    pub fn redis_huoqu_tongji_shibai(cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis获取统计信息失败，错误: {}", cuowu));
    }

    /// 数据库查询失败
    pub fn shujuku_chaxun_shibai(sql: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("数据库查询失败，SQL: {}，错误: {}", sql, cuowu));
    }
}