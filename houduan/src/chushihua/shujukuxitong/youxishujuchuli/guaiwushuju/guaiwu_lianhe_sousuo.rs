#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use super::guaiwujiegouti::{guaiwu_liebiao_jieguo, guaiwu_liebiao_xiang, guaiwu_fenlei_xinxi};
use super::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;
use serde::{Deserialize, Serialize};
use sqlx::Row;
use std::collections::hash_map::DefaultHasher;
use std::hash::{<PERSON>h, <PERSON><PERSON>};

/// 名字搜索类型枚举
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum mingzi_sousuo_leixing {
    /// 精确匹配
    jingque,
    /// 模糊匹配
    mohu,
}

/// 名字搜索条件结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingzi_sousuo_tiaojian {
    /// 搜索的名字
    pub mingzi: String,
    /// 搜索类型
    pub sousuo_leixing: mingzi_sousuo_leixing,
}

/// 怪物联合搜索参数结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_lianhe_sousuo_canshu {
    /// 元素分类（可选）
    pub yuansu_fenlei: Option<String>,
    /// 种族分类（可选）
    pub zhongzu_fenlei: Option<String>,
    /// 标志分类（可选）
    pub biaozhi_fenlei: Option<String>,
    /// AI分类（可选）
    pub ai_fenlei: Option<String>,
    /// 体型分类（可选）
    pub chicun_fenlei: Option<String>,
    /// 名字搜索（可选）
    pub mingzi_sousuo: Option<mingzi_sousuo_tiaojian>,
    /// 分页参数
    pub fenye_canshu: fenye_canshu,
}

impl guaiwu_lianhe_sousuo_canshu {
    /// 生成搜索条件的哈希值（用于缓存键）
    pub fn shengcheng_tiaojian_hash(&self) -> String {
        let mut hasher = DefaultHasher::new();
        
        // 将所有搜索条件加入哈希计算
        if let Some(ref yuansu) = self.yuansu_fenlei {
            yuansu.hash(&mut hasher);
        }
        if let Some(ref zhongzu) = self.zhongzu_fenlei {
            zhongzu.hash(&mut hasher);
        }
        if let Some(ref biaozhi) = self.biaozhi_fenlei {
            biaozhi.hash(&mut hasher);
        }
        if let Some(ref ai) = self.ai_fenlei {
            ai.hash(&mut hasher);
        }
        if let Some(ref chicun) = self.chicun_fenlei {
            chicun.hash(&mut hasher);
        }
        if let Some(ref mingzi) = self.mingzi_sousuo {
            mingzi.mingzi.hash(&mut hasher);
            format!("{:?}", mingzi.sousuo_leixing).hash(&mut hasher);
        }
        
        format!("{:x}", hasher.finish())
    }

    /// 验证搜索条件的有效性
    pub fn yanzheng_tiaojian_youxiao(&self) -> Result<(), String> {
        // 验证元素分类
        if let Some(ref yuansu) = self.yuansu_fenlei {
            if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(yuansu) {
                return Err(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(yuansu));
            }
        }

        // 验证种族分类
        if let Some(ref zhongzu) = self.zhongzu_fenlei {
            if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(zhongzu) {
                return Err(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(zhongzu));
            }
        }

        // 验证标志分类
        if let Some(ref biaozhi) = self.biaozhi_fenlei {
            if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(biaozhi) {
                return Err(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(biaozhi));
            }
        }

        // 验证AI分类
        if let Some(ref ai) = self.ai_fenlei {
            if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(ai) {
                return Err(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(ai));
            }
        }

        // 验证体型分类
        if let Some(ref chicun) = self.chicun_fenlei {
            if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(chicun) {
                return Err(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(chicun));
            }
        }

        // 验证名字搜索条件
        if let Some(ref mingzi) = self.mingzi_sousuo {
            if mingzi.mingzi.trim().is_empty() {
                return Err(guaiwu_zifuchuan_changliangguanli::cuowu_mingzi_sousuo_konggzhi.to_string());
            }
        }

        Ok(())
    }

    /// 检查是否有任何搜索条件
    pub fn you_sousuo_tiaojian(&self) -> bool {
        self.yuansu_fenlei.is_some() ||
        self.zhongzu_fenlei.is_some() ||
        self.biaozhi_fenlei.is_some() ||
        self.ai_fenlei.is_some() ||
        self.chicun_fenlei.is_some() ||
        self.mingzi_sousuo.is_some()
    }
}

/// 怪物联合搜索管理器
pub struct guaiwu_lianhe_sousuo_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<guaiwu_redis_kongzhi>,
}

impl guaiwu_lianhe_sousuo_guanli {
    /// 创建不带Redis缓存的联合搜索管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis缓存的联合搜索管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: guaiwu_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_kongzhi),
        }
    }

    /// 联合搜索怪物列表（主要入口方法）
    pub async fn lianhe_sousuo_guaiwu(&self, sousuo_canshu: &guaiwu_lianhe_sousuo_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 验证搜索条件
        if let Err(cuowu) = sousuo_canshu.yanzheng_tiaojian_youxiao() {
            return Ok(guaiwu_liebiao_jieguo::shibai(cuowu));
        }

        // 检查是否有搜索条件
        if !sousuo_canshu.you_sousuo_tiaojian() {
            let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::cuowu_lianhe_sousuo_wu_tiaojian.to_string();
            return Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi));
        }

        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_lianhe_sousuo(
                &sousuo_canshu.shengcheng_tiaojian_hash(),
                sousuo_canshu.fenye_canshu.yema,
                sousuo_canshu.fenye_canshu.meiye_shuliang,
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu_lianhe_sousuo_huancun(&huancun_jian).await {
                if let Ok(mut liebiao_jieguo) = serde_json::from_str::<guaiwu_liebiao_jieguo>(&huancun_shuju) {
                    liebiao_jieguo.shuju_laiyuan = Some("redis".to_string());
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        let jieguo = self.chaxun_shujuku_lianhe_sousuo(sousuo_canshu).await?;

        // 存储到Redis缓存
        if let Some(redis) = &self.redis_lianjie {
            if jieguo.chenggong {
                let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_lianhe_sousuo(
                    &sousuo_canshu.shengcheng_tiaojian_hash(),
                    sousuo_canshu.fenye_canshu.yema,
                    sousuo_canshu.fenye_canshu.meiye_shuliang,
                );
                
                if let Err(e) = redis.cunchu_lianhe_sousuo_huancun(&huancun_jian, &jieguo).await {
                    crate::rizhixitong::rizhixitong_jinggao_with_moshi(
                        &guaiwu_zifuchuan_changliangguanli::shengcheng_jinggao_lianhe_sousuo_huancun_cunchu_shibai(&e.to_string()),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                }
            }
        }

        Ok(jieguo)
    }

    /// 从数据库查询联合搜索结果
    async fn chaxun_shujuku_lianhe_sousuo(&self, sousuo_canshu: &guaiwu_lianhe_sousuo_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_lianhe_sousuo_zongshu(sousuo_canshu).await?;

        // 构建查询SQL和参数
        let (sql, canshu_liebiao) = guaiwu_sql_guanli::shengcheng_lianhe_sousuo_sql(sousuo_canshu);
        let pianyi = sousuo_canshu.fenye_canshu.jisuan_pianyi();

        // 执行查询
        let mut query = sqlx::query(&sql);
        
        // 绑定参数
        for canshu in &canshu_liebiao {
            query = query.bind(canshu);
        }
        
        // 绑定分页参数
        query = query.bind(sousuo_canshu.fenye_canshu.meiye_shuliang)
                    .bind(pianyi);

        match query.fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?).await {
            Ok(rows) => {
                let mut guaiwu_liebiao = Vec::new();
                
                for row in rows {
                    let guaiwu_xiang = guaiwu_liebiao_xiang {
                        guaiwu_id: row.try_get("guaiwu_id").unwrap_or(0),
                        guaiwu_mingcheng: row.try_get("guaiwu_mingcheng").unwrap_or_default(),
                        guaiwu_leiming: row.try_get("guaiwu_leiming").ok(),
                        guaiwufenlei: guaiwu_fenlei_xinxi::cong_shujuku_hang_jiexi(&row),
                    };
                    guaiwu_liebiao.push(guaiwu_xiang);
                }

                Ok(guaiwu_liebiao_jieguo::chenggong(
                    guaiwu_liebiao,
                    &sousuo_canshu.fenye_canshu,
                    zong_shuliang,
                ))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_lianhe_sousuo_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 查询联合搜索结果总数
    async fn chaxun_lianhe_sousuo_zongshu(&self, sousuo_canshu: &guaiwu_lianhe_sousuo_canshu) -> anyhow::Result<u64> {
        let (sql, canshu_liebiao) = guaiwu_sql_guanli::shengcheng_lianhe_sousuo_zongshu_sql(sousuo_canshu);
        
        let mut query = sqlx::query(&sql);
        for canshu in &canshu_liebiao {
            query = query.bind(canshu);
        }

        match query.fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?).await {
            Ok(row) => {
                let count: i64 = row.try_get("count").unwrap_or(0);
                Ok(count as u64)
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_lianhe_sousuo_zongshu_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 清除联合搜索缓存
    pub async fn qingchu_lianhe_sousuo_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis_kongzhi) = &self.redis_lianjie {
            redis_kongzhi.qingchu_lianhe_sousuo_huancun().await?;
        }
        Ok(())
    }
}
