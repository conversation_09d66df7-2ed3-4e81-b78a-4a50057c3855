#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;

/// 怪物分类信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_fenlei_xinxi {
    /// 元素分类
    pub yuansu: Vec<String>,
    /// 种族分类
    pub zhongzu: Vec<String>,
    /// 标志分类
    pub biaozhi: Vec<String>,
    /// AI分类
    pub ai: Vec<String>,
    /// 体型分类
    pub chicun: Vec<String>,
}

impl guaiwu_fenlei_xinxi {
    /// 从数据库行数据解析分类信息
    pub fn cong_shujuku_hang_jiexi(row: &sqlx::mysql::MySqlRow) -> Self {
        use sqlx::Row;

        let mut yuansu = Vec::new();
        let mut zhongzu = Vec::new();
        let mut biaozhi = Vec::new();
        let mut ai = Vec::new();
        let mut chicun = Vec::new();

        // 解析元素分类
        let yuansu_ziduan = [
            ("yuansu_wu", "无"),
            ("yuansu_shui", "水"),
            ("yuansu_di", "地"),
            ("yuansu_huo", "火"),
            ("yuansu_feng", "风"),
            ("yuansu_du", "毒"),
            ("yuansu_sheng", "圣"),
            ("yuansu_an", "暗"),
            ("yuansu_nian", "念"),
            ("yuansu_busi", "不死"),
            ("yuansu_weizhi", "未知"),
        ];

        for (ziduan_ming, zhongwen_ming) in yuansu_ziduan {
            if let Ok(zhi) = row.try_get::<String, _>(ziduan_ming) {
                if zhi == "true" || zhi == "1" {
                    yuansu.push(zhongwen_ming.to_string());
                }
            }
        }

        // 解析种族分类
        let zhongzu_ziduan = [
            ("zhongzu_formless", "无形"),
            ("zhongzu_undead", "不死"),
            ("zhongzu_brute", "动物"),
            ("zhongzu_plant", "植物"),
            ("zhongzu_insect", "昆虫"),
            ("zhongzu_fish", "鱼类"),
            ("zhongzu_demon", "恶魔"),
            ("zhongzu_human", "人类"),
            ("zhongzu_angel", "天使"),
            ("zhongzu_dragon", "龙族"),
            ("zhongzu_weizhi", "未知"),
        ];

        for (ziduan_ming, zhongwen_ming) in zhongzu_ziduan {
            if let Ok(zhi) = row.try_get::<String, _>(ziduan_ming) {
                if zhi == "true" || zhi == "1" {
                    zhongzu.push(zhongwen_ming.to_string());
                }
            }
        }

        // 解析标志分类
        let biaozhi_ziduan = [
            ("biaozhi_normal", "普通"),
            ("biaozhi_champion", "精英"),
            ("biaozhi_boss", "BOSS"),
            ("biaozhi_mvp", "MVP"),
            ("biaozhi_weizhi", "未知"),
        ];

        for (ziduan_ming, zhongwen_ming) in biaozhi_ziduan {
            if let Ok(zhi) = row.try_get::<String, _>(ziduan_ming) {
                if zhi == "true" || zhi == "1" {
                    biaozhi.push(zhongwen_ming.to_string());
                }
            }
        }

        // 解析AI分类
        let ai_ziduan = [
            ("ai_aggressive", "主动攻击"),
            ("ai_assist", "协助"),
            ("ai_looter", "拾取"),
            ("ai_cast_sensor", "施法感知"),
            ("ai_immobile", "不移动"),
            ("ai_weizhi", "未知"),
        ];

        for (ziduan_ming, zhongwen_ming) in ai_ziduan {
            if let Ok(zhi) = row.try_get::<String, _>(ziduan_ming) {
                if zhi == "true" || zhi == "1" {
                    ai.push(zhongwen_ming.to_string());
                }
            }
        }

        // 解析体型分类
        let chicun_ziduan = [
            ("chicun_small", "小型"),
            ("chicun_medium", "中型"),
            ("chicun_large", "大型"),
            ("chicun_weizhi", "未知"),
        ];

        for (ziduan_ming, zhongwen_ming) in chicun_ziduan {
            if let Ok(zhi) = row.try_get::<String, _>(ziduan_ming) {
                if zhi == "true" || zhi == "1" {
                    chicun.push(zhongwen_ming.to_string());
                }
            }
        }

        Self {
            yuansu,
            zhongzu,
            biaozhi,
            ai,
            chicun,
        }
    }
}

/// 怪物列表项信息结构体（通用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_liebiao_xiang {
    /// 怪物ID
    pub guaiwu_id: i32,
    /// 怪物名称（优先mob_name表的schinese，没有则用汇总表的zhongwenming）
    pub guaiwu_mingcheng: String,
    /// 怪物类名（从guaiwu_huizong表的dbname字段获取）
    pub guaiwu_leiming: Option<String>,
    /// 怪物分类信息
    pub guaiwufenlei: guaiwu_fenlei_xinxi,
}

/// 怪物列表查询结果结构体（通用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 怪物列表
    pub guaiwu_liebiao: Vec<guaiwu_liebiao_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 数据来源（用于测试显示）
    pub shuju_laiyuan: Option<String>,
}

impl guaiwu_liebiao_jieguo {
    /// 创建成功的查询结果
    pub fn chenggong(
        guaiwu_liebiao: Vec<guaiwu_liebiao_xiang>,
        fenye_canshu: &crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu,
        zong_shuliang: u64,
    ) -> Self {
        let zong_yeshu = fenye_canshu.jisuan_zong_yeshu(zong_shuliang);
        Self {
            chenggong: true,
            cuowu_xinxi: None,
            guaiwu_liebiao,
            dangqian_yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            shuju_laiyuan: Some("mysql".to_string()),
        }
    }

    /// 创建失败的查询结果
    pub fn shibai(cuowu_xinxi: String) -> Self {
        Self {
            chenggong: false,
            cuowu_xinxi: Some(cuowu_xinxi),
            guaiwu_liebiao: Vec::new(),
            dangqian_yema: 1,
            meiye_shuliang: 10,
            zong_shuliang: 0,
            zong_yeshu: 1,
            shuju_laiyuan: None,
        }
    }
}

/// mob_name表基础信息结构体
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct guaiwu_jiben_xinxi {
    /// 怪物ID
    #[sqlx(rename = "ID")]
    pub id: i32,
    /// Aegis名称
    #[sqlx(rename = "Aegis_name")]
    pub aegis_name: Option<String>,
    /// 类型
    #[sqlx(rename = "Type")]
    pub type_field: Option<String>,
    /// 简体中文名
    pub schinese: Option<String>,
    /// 繁体中文名
    pub tchinese: Option<String>,
    /// 英文名
    pub en: Option<String>,
    /// 日文名
    pub jp: Option<String>,
    /// 韩文名
    pub kr: Option<String>,
}

/// guaiwu_huizong表汇总信息结构体
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct guaiwu_huizong_xinxi {
    /// 怪物ID
    pub id: Option<i32>,
    /// 数据库名称
    pub dbname: Option<String>,
    /// 中文名
    pub zhongwenming: Option<String>,
    /// 小尺寸
    pub chicun_small: Option<String>,
    /// 中尺寸
    pub chicun_medium: Option<String>,
    /// 大尺寸
    pub chicun_large: Option<String>,
    /// 未知尺寸
    pub chicun_weizhi: Option<String>,
    /// 无元素
    pub yuansu_wu: Option<String>,
    /// 水元素
    pub yuansu_shui: Option<String>,
    /// 地元素
    pub yuansu_di: Option<String>,
    /// 火元素
    pub yuansu_huo: Option<String>,
    /// 风元素
    pub yuansu_feng: Option<String>,
    /// 毒元素
    pub yuansu_du: Option<String>,
    /// 圣元素
    pub yuansu_sheng: Option<String>,
    /// 暗元素
    pub yuansu_an: Option<String>,
    /// 念元素
    pub yuansu_nian: Option<String>,
    /// 不死元素
    pub yuansu_busi: Option<String>,
    /// 未知元素
    pub yuansu_weizhi: Option<String>,
    /// 无形种族
    pub zhongzu_formless: Option<String>,
    /// 不死种族
    pub zhongzu_undead: Option<String>,
    /// 动物种族
    pub zhongzu_brute: Option<String>,
    /// 植物种族
    pub zhongzu_plant: Option<String>,
    /// 昆虫种族
    pub zhongzu_insect: Option<String>,
    /// 鱼类种族
    pub zhongzu_fish: Option<String>,
    /// 恶魔种族
    pub zhongzu_demon: Option<String>,
    /// 人类种族
    pub zhongzu_human: Option<String>,
    /// 天使种族
    pub zhongzu_angel: Option<String>,
    /// 龙族种族
    pub zhongzu_dragon: Option<String>,
    /// 未知种族
    pub zhongzu_weizhi: Option<String>,
    /// 普通标志
    pub biaozhi_normal: Option<String>,
    /// 精英标志
    pub biaozhi_champion: Option<String>,
    /// BOSS标志
    pub biaozhi_boss: Option<String>,
    /// MVP标志
    pub biaozhi_mvp: Option<String>,
    /// 未知标志
    pub biaozhi_weizhi: Option<String>,
    /// 主动攻击AI
    pub ai_aggressive: Option<String>,
    /// 协助AI
    pub ai_assist: Option<String>,
    /// 拾取AI
    pub ai_looter: Option<String>,
    /// 施法感知AI
    pub ai_cast_sensor: Option<String>,
    /// 不移动AI
    pub ai_immobile: Option<String>,
    /// 未知AI
    pub ai_weizhi: Option<String>,
    /// 等级
    pub level: Option<i32>,
    /// 生命值
    pub health: Option<i32>,
    /// 基础经验
    pub base_experience: Option<i32>,
    /// 职业经验
    pub job_experience: Option<i32>,
    /// MVP标记
    pub mvp_flag: Option<i32>,
    /// 基础信息yaml
    pub jichuxinxi_yaml: Option<String>,
    /// 主属性yaml
    pub zhushuxing_yaml: Option<String>,
    /// AI行为yaml
    pub ai_behaviors_yaml: Option<String>,
    /// 元素抗性yaml
    pub yuansukangxing_yaml: Option<String>,
    /// 分类信息yaml
    pub fenlei_xinxi_yaml: Option<String>,
    /// 普通掉落物yaml
    pub putong_diaoluowu_yaml: Option<String>,
    /// MVP掉落物yaml
    pub mvp_diaoluowu_yaml: Option<String>,
    /// 地图掉落物yaml
    pub ditu_diaoluowu_yaml: Option<String>,
    /// 技能列表yaml
    pub jineng_liebiao_yaml: Option<String>,
    /// 地图列表yaml
    pub ditu_liebiao_yaml: Option<String>,
    /// 经验信息yaml
    pub jingyan_xinxi_yaml: Option<String>,
    /// 任务列表yaml
    pub renwu_liebiao_yaml: Option<String>,
    /// 宠物信息yaml
    pub chongwu_xinxi_yaml: Option<String>,
    /// 掉落物数量
    pub diaoluowu_shuliang: Option<String>,
    /// 技能数量
    pub jineng_shuliang: Option<String>,
    /// 地图数量
    pub ditu_shuliang: Option<String>,
    /// 任务数量
    pub renwu_shuliang: Option<String>,
    /// 是宠物
    pub shi_chongwu: Option<String>,
    /// 有基础信息
    pub you_jichuxinxi: Option<String>,
    /// 有掉落物
    pub you_diaoluowu: Option<String>,
    /// 有技能
    pub you_jineng: Option<String>,
    /// 有地图
    pub you_ditu: Option<String>,
    /// 有经验
    pub you_jingyan: Option<String>,
    /// 有任务
    pub you_renwu: Option<String>,
    /// 有宠物
    pub you_chongwu: Option<String>,
    /// 精英怪
    pub jingyingguai: Option<String>,
}

/// 完整怪物信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_wanzheng_xinxi {
    /// 怪物ID
    pub id: i32,
    /// 基础信息
    pub jiben_xinxi: Option<guaiwu_jiben_xinxi>,
    /// 汇总信息
    pub huizong_xinxi: Option<guaiwu_huizong_xinxi>,
}

/// 怪物查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 数据来源（mysql/redis）
    pub laiyuan: String,
    /// 完整怪物信息（当查询全部信息时）
    pub wanzheng_xinxi: Option<guaiwu_wanzheng_xinxi>,
    /// 怪物数据映射（当查询指定字段时）
    pub guaiwu_shuju: Option<HashMap<String, String>>,
}

/// 怪物分类查询参数结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_fenlei_chaxun_canshu {
    /// 分类类型（如：yuansu_huo, zhongzu_human, biaozhi_boss等）
    pub fenlei_leixing: String,
    /// 分页参数
    pub fenye_canshu: crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu,
}

impl guaiwu_fenlei_chaxun_canshu {
    /// 创建新的分类查询参数
    pub fn new(fenlei_leixing: String, yema: u32, meiye_shuliang: u32) -> Self {
        Self {
            fenlei_leixing,
            fenye_canshu: crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu::new(yema, meiye_shuliang),
        }
    }
}

/// 宏：简化字符串向量创建
macro_rules! ziduan_liebiao {
    ($($ziduan:expr),* $(,)?) => {
        vec![$($ziduan.to_string()),*]
    };
}

/// 支持的字段映射
pub struct guaiwu_ziduan_yingshe;

impl guaiwu_ziduan_yingshe {
    /// 获取mob_name表支持的字段
    pub fn huoqu_jiben_biao_ziduan() -> Vec<String> {
        ziduan_liebiao![
            "ID",
            "Aegis_name",
            "Type",
            "schinese",
            "tchinese",
            "en",
            "jp",
            "kr"
        ]
    }

    /// 获取guaiwu_huizong表支持的字段
    pub fn huoqu_huizong_biao_ziduan() -> Vec<String> {
        ziduan_liebiao![
            "id", "dbname", "zhongwenming",
            // 尺寸相关字段
            "chicun_small", "chicun_medium", "chicun_large", "chicun_weizhi",
            // 元素相关字段
            "yuansu_wu", "yuansu_shui", "yuansu_di", "yuansu_huo", "yuansu_feng",
            "yuansu_du", "yuansu_sheng", "yuansu_an", "yuansu_nian", "yuansu_busi", "yuansu_weizhi",
            // 种族相关字段
            "zhongzu_formless", "zhongzu_undead", "zhongzu_brute", "zhongzu_plant", "zhongzu_insect",
            "zhongzu_fish", "zhongzu_demon", "zhongzu_human", "zhongzu_angel", "zhongzu_dragon", "zhongzu_weizhi",
            // 标志相关字段
            "biaozhi_normal", "biaozhi_champion", "biaozhi_boss", "biaozhi_mvp", "biaozhi_weizhi",
            // AI相关字段
            "ai_aggressive", "ai_assist", "ai_looter", "ai_cast_sensor", "ai_immobile", "ai_weizhi",
            // 基础属性字段
            "level", "health", "base_experience", "job_experience", "mvp_flag",
            // YAML数据字段
            "jichuxinxi_yaml", "zhushuxing_yaml", "ai_behaviors_yaml", "yuansukangxing_yaml", "fenlei_xinxi_yaml",
            "putong_diaoluowu_yaml", "mvp_diaoluowu_yaml", "ditu_diaoluowu_yaml", "jineng_liebiao_yaml",
            "ditu_liebiao_yaml", "jingyan_xinxi_yaml", "renwu_liebiao_yaml", "chongwu_xinxi_yaml",
            // 数量统计字段
            "diaoluowu_shuliang", "jineng_shuliang", "ditu_shuliang", "renwu_shuliang",
            // 布尔标记字段
            "shi_chongwu", "you_jichuxinxi", "you_diaoluowu", "you_jineng", "you_ditu",
            "you_jingyan", "you_renwu", "you_chongwu", "jingyingguai"
        ]
    }

    /// 获取所有支持的字段映射
    pub fn huoqu_suoyou_ziduan_yingshe() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert("mob_name".to_string(), Self::huoqu_jiben_biao_ziduan());
        ziduan_map.insert("guaiwu_huizong".to_string(), Self::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 检查字段是否有效
    pub fn jiancha_ziduan_youxiao(ziduan_ming: &str) -> bool {
        let suoyou_ziduan = Self::huoqu_suoyou_ziduan_yingshe();
        for (_biao_ming, ziduan_liebiao) in suoyou_ziduan {
            if ziduan_liebiao.contains(&ziduan_ming.to_string()) {
                return true;
            }
        }
        false
    }

    /// 检查字段属于哪个表
    /// 返回：Some("mob_name") 或 Some("guaiwu_huizong") 或 None
    pub fn jiancha_ziduan_suoshu_biao(ziduan_ming: &str) -> Option<String> {
        if Self::huoqu_jiben_biao_ziduan().contains(&ziduan_ming.to_string()) {
            return Some("mob_name".to_string());
        }
        if Self::huoqu_huizong_biao_ziduan().contains(&ziduan_ming.to_string()) {
            return Some("guaiwu_huizong".to_string());
        }
        None
    }

    /// 动态验证字段列表，返回有效字段和它们所属的表
    /// 返回：(有效的mob_name字段, 有效的guaiwu_huizong字段, 无效字段)
    pub fn dongtai_yanzheng_ziduan_liebiao(ziduan_liebiao: &[&str]) -> (Vec<String>, Vec<String>, Vec<String>) {
        let mut jiben_biao_ziduan = Vec::new();
        let mut huizong_biao_ziduan = Vec::new();
        let mut wuxiao_ziduan = Vec::new();

        for ziduan in ziduan_liebiao {
            match Self::jiancha_ziduan_suoshu_biao(ziduan) {
                Some(biao_ming) => {
                    if biao_ming == "mob_name" {
                        jiben_biao_ziduan.push(ziduan.to_string());
                    } else if biao_ming == "guaiwu_huizong" {
                        huizong_biao_ziduan.push(ziduan.to_string());
                    }
                }
                None => {
                    wuxiao_ziduan.push(ziduan.to_string());
                }
            }
        }

        (jiben_biao_ziduan, huizong_biao_ziduan, wuxiao_ziduan)
    }
}
