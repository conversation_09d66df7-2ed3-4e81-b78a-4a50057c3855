#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::guaiwushuju_liebiao_chaxun::guaiwu_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;

/// 怪物列表查询测试类
pub struct guaiwu_liebiao_ceshi;

impl guaiwu_liebiao_ceshi {
    /// 测试怪物列表查询（仅MySQL）
    pub async fn ceshi_liebiao_chaxun_mysql() -> anyhow::Result<()> {
        println!("开始测试怪物列表查询（仅MySQL）...");

        // 创建MySQL连接 - 使用简化版初始化
        let mysql_lianjie = crate::chushihua::shujukuxitong::mysqlshujuku::mysql_chushihua::mysql_chushihua::chushihua_jiandan(
            "localhost", 3306, "root", "123456",
        ).await?;

        // 创建查询管理器
        let liebiao_guanli = guaiwu_liebiao_chaxun_guanli::new(mysql_lianjie);

        // 测试分页参数
        let fenye_canshu = fenye_canshu::new(1, 10);

        // 执行查询
        match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
            Ok(jieguo) => {
                println!("查询成功！");
                println!("成功状态: {}", jieguo.chenggong);
                println!("当前页码: {}", jieguo.dangqian_yema);
                println!("每页数量: {}", jieguo.meiye_shuliang);
                println!("总数量: {}", jieguo.zong_shuliang);
                println!("总页数: {}", jieguo.zong_yeshu);
                println!("数据来源: {:?}", jieguo.shuju_laiyuan);
                println!("怪物列表数量: {}", jieguo.guaiwu_liebiao.len());

                // 显示前5个怪物
                for (i, guaiwu) in jieguo.guaiwu_liebiao.iter().take(5).enumerate() {
                    println!("  {}. ID: {}, 名称: {}", i + 1, guaiwu.guaiwu_id, guaiwu.guaiwu_mingcheng);
                }

                if let Some(cuowu) = jieguo.cuowu_xinxi {
                    println!("错误信息: {}", cuowu);
                }
            }
            Err(e) => {
                println!("查询失败: {}", e);
                return Err(e);
            }
        }

        println!("怪物列表查询测试完成！");
        Ok(())
    }

    /// 测试怪物列表查询（MySQL + Redis）
    pub async fn ceshi_liebiao_chaxun_redis() -> anyhow::Result<()> {
        println!("开始测试怪物列表查询（MySQL + Redis）...");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new().await?;

        // 创建Redis连接
        let redis_lianjie = redis_lianjie_guanli::new().await?;

        // 创建查询管理器
        let liebiao_guanli = guaiwu_liebiao_chaxun_guanli::new_with_redis(mysql_lianjie, redis_lianjie);

        // 测试分页参数
        let fenye_canshu = fenye_canshu::new(1, 5);

        // 第一次查询（从数据库）
        println!("第一次查询（应该从数据库获取）...");
        match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
            Ok(jieguo) => {
                println!("第一次查询成功！数据来源: {:?}", jieguo.shuju_laiyuan);
                println!("怪物数量: {}", jieguo.guaiwu_liebiao.len());
            }
            Err(e) => {
                println!("第一次查询失败: {}", e);
                return Err(e);
            }
        }

        // 第二次查询（应该从Redis缓存）
        println!("第二次查询（应该从Redis缓存获取）...");
        match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
            Ok(jieguo) => {
                println!("第二次查询成功！数据来源: {:?}", jieguo.shuju_laiyuan);
                println!("怪物数量: {}", jieguo.guaiwu_liebiao.len());
            }
            Err(e) => {
                println!("第二次查询失败: {}", e);
                return Err(e);
            }
        }

        // 测试缓存统计
        match liebiao_guanli.huoqu_liebiao_huancun_tongji().await {
            Ok(tongji) => {
                println!("缓存统计: {}", tongji);
            }
            Err(e) => {
                println!("获取缓存统计失败: {}", e);
            }
        }

        println!("怪物列表查询（Redis）测试完成！");
        Ok(())
    }

    /// 测试多页查询
    pub async fn ceshi_duoye_chaxun() -> anyhow::Result<()> {
        println!("开始测试多页查询...");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new().await?;

        // 创建查询管理器
        let liebiao_guanli = guaiwu_liebiao_chaxun_guanli::new(mysql_lianjie);

        // 测试不同页码
        for yema in 1..=3 {
            let fenye_canshu = fenye_canshu::new(yema, 5);

            match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
                Ok(jieguo) => {
                    println!("第{}页查询成功！", yema);
                    println!("  当前页: {}/{}", jieguo.dangqian_yema, jieguo.zong_yeshu);
                    println!("  本页怪物数量: {}", jieguo.guaiwu_liebiao.len());

                    if !jieguo.guaiwu_liebiao.is_empty() {
                        let first = &jieguo.guaiwu_liebiao[0];
                        let last = jieguo.guaiwu_liebiao.last().unwrap();
                        println!("  ID范围: {} - {}", first.guaiwu_id, last.guaiwu_id);
                    }
                }
                Err(e) => {
                    println!("第{}页查询失败: {}", yema, e);
                }
            }
        }

        println!("多页查询测试完成！");
        Ok(())
    }

    /// 运行所有测试
    pub async fn yunxing_suoyou_ceshi() -> anyhow::Result<()> {
        println!("=== 开始怪物列表查询测试 ===");

        // 测试MySQL查询
        if let Err(e) = Self::ceshi_liebiao_chaxun_mysql().await {
            println!("MySQL测试失败: {}", e);
        }

        println!();

        // 测试Redis查询
        if let Err(e) = Self::ceshi_liebiao_chaxun_redis().await {
            println!("Redis测试失败: {}", e);
        }

        println!();

        // 测试多页查询
        if let Err(e) = Self::ceshi_duoye_chaxun().await {
            println!("多页查询测试失败: {}", e);
        }

        println!("=== 怪物列表查询测试完成 ===");
        Ok(())
    }
}
