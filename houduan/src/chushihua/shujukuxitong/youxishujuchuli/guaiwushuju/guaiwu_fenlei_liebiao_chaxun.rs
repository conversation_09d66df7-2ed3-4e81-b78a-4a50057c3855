#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use super::guaiwujiegouti::{guaiwu_fenlei_chaxun_canshu, guaiwu_fenlei_xinxi, guaiwu_liebiao_jieguo, guaiwu_liebiao_xiang};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use sqlx::Row;

/// 怪物分类列表查询管理器
pub struct guaiwu_fenlei_liebiao_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwu_fenlei_liebiao_chaxun_guanli {
    /// 创建新的怪物分类列表查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的怪物分类列表查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 获取指定分类的怪物列表（分页）
    pub async fn huoqu_fenlei_guaiwu_liebiao(&self, chaxun_canshu: &guaiwu_fenlei_chaxun_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 验证分类类型是否有效
        if !guaiwu_sql_guanli::yanzheng_fenlei_leixing_youxiao(&chaxun_canshu.fenlei_leixing) {
            let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_wuxiao_fenlei_leixing(&chaxun_canshu.fenlei_leixing);
            return Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi));
        }

        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_liebiao(
                &chaxun_canshu.fenlei_leixing,
                chaxun_canshu.fenye_canshu.yema,
                chaxun_canshu.fenye_canshu.meiye_shuliang,
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(mut liebiao_jieguo) = serde_json::from_str::<guaiwu_liebiao_jieguo>(&huancun_shuju) {
                    // 标记数据来源为Redis缓存
                    liebiao_jieguo.shuju_laiyuan = Some("redis".to_string());
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        match self.chaxun_shujuku_fenlei_liebiao(chaxun_canshu).await {
            Ok(liebiao_jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_liebiao(
                        &chaxun_canshu.fenlei_leixing,
                        chaxun_canshu.fenye_canshu.yema,
                        chaxun_canshu.fenye_canshu.meiye_shuliang,
                    );

                    if let Ok(json_shuju) = serde_json::to_string(&liebiao_jieguo) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, guaiwu_zifuchuan_changliangguanli::fenlei_liebiao_huancun_shijian as i64).await;
                    }
                }

                Ok(liebiao_jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_fenlei_liebiao_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 从数据库查询指定分类的怪物列表
    async fn chaxun_shujuku_fenlei_liebiao(&self, chaxun_canshu: &guaiwu_fenlei_chaxun_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_fenlei_guaiwu_zongshu(&chaxun_canshu.fenlei_leixing).await?;

        // 查询分页数据
        let sql = guaiwu_sql_guanli::shengcheng_sql_fenlei_chaxun_liebiao(&chaxun_canshu.fenlei_leixing);
        let pianyi = chaxun_canshu.fenye_canshu.jisuan_pianyi();

        match sqlx::query(&sql)
            .bind(chaxun_canshu.fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut guaiwu_liebiao = Vec::new();

                for row in rows {
                    // 尝试获取怪物ID，如果失败则跳过这行
                    if let Ok(guaiwu_id) = row.try_get::<i32, _>("guaiwu_id") {
                        // 获取怪物名称，如果为NULL则使用默认值
                        let guaiwu_mingcheng = row.try_get::<Option<String>, _>("guaiwu_mingcheng")
                            .unwrap_or(None)
                            .unwrap_or_else(|| format!("怪物_{}", guaiwu_id));

                        // 获取其他可选字段
                        let guaiwu_leiming = row.try_get::<Option<String>, _>("guaiwu_leiming").unwrap_or(None);
                        let guaiwufenlei = guaiwu_fenlei_xinxi::cong_shujuku_hang_jiexi(&row);

                        guaiwu_liebiao.push(guaiwu_liebiao_xiang {
                            guaiwu_id,
                            guaiwu_mingcheng,
                            guaiwu_leiming,
                            guaiwufenlei,
                        });
                    }
                }

                Ok(guaiwu_liebiao_jieguo::chenggong(guaiwu_liebiao, &chaxun_canshu.fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_fenlei_liebiao_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询指定分类的怪物总数
    async fn chaxun_fenlei_guaiwu_zongshu(&self, fenlei_leixing: &str) -> anyhow::Result<u64> {
        let sql = guaiwu_sql_guanli::shengcheng_sql_fenlei_chaxun_zongshu(fenlei_leixing);

        match sqlx::query(&sql)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 获取分类列表缓存统计信息
    pub async fn huoqu_fenlei_liebiao_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.huoqu_fenlei_liebiao_huancun_tongji().await
        } else {
            Ok(guaiwu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }

    /// 清除分类列表缓存
    pub async fn qingchu_fenlei_liebiao_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.qingchu_fenlei_liebiao_huancun().await
        } else {
            Ok(())
        }
    }

    /// 获取所有支持的分类类型列表
    pub fn huoqu_zhichi_fenlei_leixing_liebiao() -> Vec<String> {
        vec![
            // 元素分类
            "yuansu_wu".to_string(), "yuansu_shui".to_string(), "yuansu_di".to_string(), 
            "yuansu_huo".to_string(), "yuansu_feng".to_string(), "yuansu_du".to_string(), 
            "yuansu_sheng".to_string(), "yuansu_an".to_string(), "yuansu_nian".to_string(), 
            "yuansu_busi".to_string(), "yuansu_weizhi".to_string(),
            // 种族分类
            "zhongzu_formless".to_string(), "zhongzu_undead".to_string(), "zhongzu_brute".to_string(), 
            "zhongzu_plant".to_string(), "zhongzu_insect".to_string(), "zhongzu_fish".to_string(), 
            "zhongzu_demon".to_string(), "zhongzu_human".to_string(), "zhongzu_angel".to_string(), 
            "zhongzu_dragon".to_string(), "zhongzu_weizhi".to_string(),
            // 标志分类
            "biaozhi_normal".to_string(), "biaozhi_champion".to_string(), "biaozhi_boss".to_string(), 
            "biaozhi_mvp".to_string(), "biaozhi_weizhi".to_string(),
            // AI分类
            "ai_aggressive".to_string(), "ai_assist".to_string(), "ai_looter".to_string(), 
            "ai_cast_sensor".to_string(), "ai_immobile".to_string(), "ai_weizhi".to_string(),
            // 体型分类
            "chicun_small".to_string(), "chicun_medium".to_string(), "chicun_large".to_string(), 
            "chicun_weizhi".to_string(),
        ]
    }
}
