#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use super::guaiwujiegouti::{guaiwu_fenlei_xinxi, guaiwu_liebiao_jieguo, guaiwu_liebiao_xiang};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;
use sqlx::Row;

/// 怪物列表查询管理器
pub struct guaiwu_liebiao_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwu_liebiao_chaxun_guanli {
    /// 创建新的怪物列表查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的怪物列表查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 获取怪物列表（分页）
    pub async fn huoqu_guaiwu_liebiao(&self, fenye_canshu: &fenye_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_liebiao_fenye(
                fenye_canshu.yema,
                fenye_canshu.meiye_shuliang,
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(mut liebiao_jieguo) = serde_json::from_str::<guaiwu_liebiao_jieguo>(&huancun_shuju) {
                    // 标记数据来源为Redis缓存
                    liebiao_jieguo.shuju_laiyuan = Some("redis".to_string());
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        match self.chaxun_shujuku_liebiao(fenye_canshu).await {
            Ok(liebiao_jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_liebiao_fenye(
                        fenye_canshu.yema,
                        fenye_canshu.meiye_shuliang,
                    );

                    if let Ok(json_shuju) = serde_json::to_string(&liebiao_jieguo) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, guaiwu_zifuchuan_changliangguanli::liebiao_huancun_shijian as i64).await;
                    }
                }

                Ok(liebiao_jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_liebiao_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 从数据库查询怪物列表
    async fn chaxun_shujuku_liebiao(&self, fenye_canshu: &fenye_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_guaiwu_zongshu().await?;

        // 查询分页数据
        let sql = guaiwu_sql_guanli::sql_chaxun_liebiao_fenye;
        let pianyi = fenye_canshu.jisuan_pianyi();

        match sqlx::query(sql)
            .bind(fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut guaiwu_liebiao = Vec::new();
                let zong_hang_shu = rows.len();

                for (index, row) in rows.iter().enumerate() {
                    if let Ok(guaiwu_id) = row.try_get::<i32, _>("guaiwu_id") {
                        // 处理可能为NULL的guaiwu_mingcheng字段
                        let guaiwu_mingcheng = row.try_get::<Option<String>, _>("guaiwu_mingcheng")
                            .unwrap_or(None)
                            .unwrap_or_else(|| format!("怪物{}", guaiwu_id));

                        let guaiwu_leiming = row.try_get::<String, _>("guaiwu_leiming").ok();
                        let guaiwufenlei = guaiwu_fenlei_xinxi::cong_shujuku_hang_jiexi(&row);

                        guaiwu_liebiao.push(guaiwu_liebiao_xiang {
                            guaiwu_id,
                            guaiwu_mingcheng,
                            guaiwu_leiming,
                            guaiwufenlei,
                        });
                    } else {
                        // 记录跳过的行信息
                        crate::rizhixitong::rizhixitong_jinggao_with_moshi(
                            &format!("跳过第{}行记录: guaiwu_id解析失败", index + 1),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }

                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("怪物列表查询: 数据库返回{}行，成功解析{}行", zong_hang_shu, guaiwu_liebiao.len()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                Ok(guaiwu_liebiao_jieguo::chenggong(guaiwu_liebiao, fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_liebiao_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询怪物总数
    async fn chaxun_guaiwu_zongshu(&self) -> anyhow::Result<u64> {
        let sql = guaiwu_sql_guanli::sql_chaxun_liebiao_zongshu;

        match sqlx::query(sql)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 获取怪物列表缓存统计信息
    pub async fn huoqu_liebiao_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.huoqu_liebiao_huancun_tongji().await
        } else {
            Ok(guaiwu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }

    /// 清除怪物列表缓存
    pub async fn qingchu_liebiao_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.qingchu_liebiao_huancun().await
        } else {
            Ok(())
        }
    }
}
