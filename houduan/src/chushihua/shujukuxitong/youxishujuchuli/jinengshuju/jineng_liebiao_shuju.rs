#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_redis_kongzhi::jineng_redis_kongzhi;
use super::jineng_rizhi_kongzhi::jineng_zifuchuan_changliangguanli;
use super::jineng_sql_kongzhi::jineng_sql_guanli;
use super::jinengshujujiegouti::{
    jineng_liebiao_fenye_canshu, jineng_liebiao_fenye_xinxi,
    jineng_liebiao_jieguo, jineng_liebiao_xiang,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use sqlx::Row;
use std::collections::HashMap;

/// 技能列表数据管理器
pub struct jineng_liebiao_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_kongzhi: Option<jineng_redis_kongzhi>,
}

impl jineng_liebiao_guanli {
    /// 创建新的技能列表管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的技能列表管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: jineng_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取技能列表（带Redis缓存）
    pub async fn huoqu_jineng_liebiao(&self, fenye_canshu: jineng_liebiao_fenye_canshu) -> anyhow::Result<jineng_liebiao_jieguo> {
        // 参数验证
        if fenye_canshu.meiye_shuliang == 0 || fenye_canshu.dangqian_ye == 0 {
            return Ok(jineng_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::cuowu_canshu_wuxiao.to_string()),
                laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                jineng_liebiao: Vec::new(),
                fenye_xinxi: jineng_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu: 0,
                    zong_jineng_shu: 0,
                    dangqianye_jineng_shu: 0,
                },
            });
        }

        // 如果有Redis控制器，先尝试从Redis获取
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(mut liebiao_jieguo)) = redis_kongzhi.huoqu_jineng_liebiao(
                fenye_canshu.meiye_shuliang,
                fenye_canshu.dangqian_ye,
            ).await {
                // 设置数据来源为Redis
                liebiao_jieguo.laiyuan = Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_redis.to_string());
                return Ok(liebiao_jieguo);
            }
        }

        // Redis中没有数据，从MySQL查询
        let liebiao_jieguo = self.chaxun_jineng_liebiao_from_mysql(&fenye_canshu).await?;

        // 如果有Redis控制器，将查询结果存储到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if liebiao_jieguo.chenggong {
                let _ = redis_kongzhi.cunchu_jineng_liebiao(
                    fenye_canshu.meiye_shuliang,
                    fenye_canshu.dangqian_ye,
                    &liebiao_jieguo,
                ).await;
            }
        }

        Ok(liebiao_jieguo)
    }

    /// 从MySQL查询技能列表
    async fn chaxun_jineng_liebiao_from_mysql(&self, fenye_canshu: &jineng_liebiao_fenye_canshu) -> anyhow::Result<jineng_liebiao_jieguo> {
        // 获取技能总数
        let zong_jineng_shu = self.huoqu_jineng_zongshu().await?;

        // 计算分页信息
        let zongyeshu = if zong_jineng_shu == 0 {
            0
        } else {
            ((zong_jineng_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查当前页是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(jineng_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(fenye_canshu.dangqian_ye, zongyeshu)),
                laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                jineng_liebiao: Vec::new(),
                fenye_xinxi: jineng_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_jineng_shu,
                    dangqianye_jineng_shu: 0,
                },
            });
        }

        // 计算偏移量
        let offset = (fenye_canshu.dangqian_ye - 1) * fenye_canshu.meiye_shuliang;

        // 查询技能列表
        let mut jineng_liebiao = self.chaxun_huizong_liebiao(fenye_canshu.meiye_shuliang, offset).await?;

        // 获取技能ID列表，用于查询name表
        let id_liebiao: Vec<i32> = jineng_liebiao.iter()
            .map(|item| item.jineng_id)
            .collect();

        // 查询name表获取优先名称
        let mingcheng_map = self.chaxun_name_biao_mingcheng(&id_liebiao).await?;

        // 更新技能名称（优先使用name表的schinese）
        for item in &mut jineng_liebiao {
            if let Some(schinese) = mingcheng_map.get(&item.jineng_id) {
                if !schinese.is_empty() {
                    item.jineng_mingcheng = schinese.clone();
                }
            }
        }

        let dangqianye_jineng_shu = jineng_liebiao.len() as u32;

        Ok(jineng_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
            jineng_liebiao,
            fenye_xinxi: jineng_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_jineng_shu,
                dangqianye_jineng_shu,
            },
        })
    }

    /// 获取技能总数
    async fn huoqu_jineng_zongshu(&self) -> anyhow::Result<u64> {
        let row = sqlx::query(jineng_sql_guanli::sql_huoqu_jineng_zongshu)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let total: i64 = row.get("total");
        Ok(total as u64)
    }

    /// 查询汇总表技能列表
    async fn chaxun_huizong_liebiao(&self, limit: u32, offset: u32) -> anyhow::Result<Vec<jineng_liebiao_xiang>> {
        let sql = jineng_sql_guanli::shengcheng_sql_jineng_liebiao_fenye(limit, offset);

        let rows = sqlx::query(&sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut liebiao = Vec::new();
        for row in rows {
            let jineng_id: Option<i32> = row.try_get("jineng_id").unwrap_or(None);
            let jineng_mingcheng: Option<String> = row.try_get("jineng_mingcheng").unwrap_or(None);
            let leiming: Option<String> = row.try_get("leiming").unwrap_or(None);

            if let Some(id) = jineng_id {
                liebiao.push(jineng_liebiao_xiang {
                    jineng_id: id,
                    jineng_mingcheng: jineng_mingcheng.unwrap_or_default(),
                    leiming,
                });
            }
        }

        Ok(liebiao)
    }

    /// 查询name表获取技能名称
    async fn chaxun_name_biao_mingcheng(&self, id_liebiao: &[i32]) -> anyhow::Result<HashMap<i32, String>> {
        if id_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = jineng_sql_guanli::shengcheng_sql_piliang_chaxun_jineng_mingcheng(id_liebiao);

        let rows = sqlx::query(&sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut mingcheng_map = HashMap::new();
        for row in rows {
            let id: Option<i32> = row.try_get("ID").unwrap_or(None);
            let schinese: Option<String> = row.try_get("schinese").unwrap_or(None);

            if let (Some(id), Some(schinese)) = (id, schinese) {
                if !schinese.is_empty() {
                    mingcheng_map.insert(id, schinese);
                }
            }
        }

        Ok(mingcheng_map)
    }

    /// 清理技能列表的Redis缓存
    pub async fn qingchu_jineng_liebiao_huancun(&self) -> anyhow::Result<u64> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.qingchu_jineng_liebiao_huancun().await
            }
            None => Ok(0)
        }
    }

    /// 获取技能列表缓存统计信息
    pub async fn huoqu_liebiao_huancun_tongji(&self) -> anyhow::Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                let moshi = jineng_zifuchuan_changliangguanli::redis_jian_moshi_jineng_liebiao;
                match redis_kongzhi.redis_lianjie.count_keys_by_pattern(moshi).await {
                    Ok(shuliang) => {
                        Ok(jineng_zifuchuan_changliangguanli::shengcheng_tongji_jineng_liebiao_huancun(shuliang))
                    }
                    Err(e) => Err(e)
                }
            }
            None => Ok(jineng_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }
}
