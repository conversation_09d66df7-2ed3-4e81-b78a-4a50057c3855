#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 技能数据处理SQL语句管理类
pub struct jineng_sql_guanli;

impl jineng_sql_guanli {
    // ==================== 基础查询SQL ====================

    /// 查询skill_name表基础信息的SQL
    pub const sql_chaxun_jiben_xinxi: &'static str =
        "SELECT ID, Aegis_name, name, schinese, tchinese, jp, kr FROM skill_name WHERE ID = ?";

    /// 查询jineng_huizong表汇总信息的SQL
    pub const sql_chaxun_huizong_xinxi: &'static str =
        "SELECT jineng_id, jineng_mingcheng, wupin_shuliang, zhiye_shuliang, lishibiandong_shuliang, 
         zhongwenming, leiming, tupian_url, zuida_dengji, jichuxinxi_yaml, wupin_yaml, zhiye_yaml, 
         lishibiandong_yaml, you_jichu<PERSON><PERSON>, you_wupin, you_zhiye, you_lishibiandong 
         FROM jineng_huizong WHERE jineng_id = ?";

    // ==================== 存在性检查SQL ====================

    /// 检查skill_name表中技能是否存在的SQL
    pub const sql_jiancha_jiben_biao_cunzai: &'static str =
        "SELECT COUNT(*) as count FROM skill_name WHERE ID = ?";

    /// 检查jineng_huizong表中技能是否存在的SQL
    pub const sql_jiancha_huizong_biao_cunzai: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE jineng_id = ?";

    // ==================== 动态SQL生成方法 ====================

    /// 生成查询skill_name表指定字段的SQL
    pub fn shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM skill_name WHERE ID = ?", ziduan_str)
    }

    /// 生成查询jineng_huizong表指定字段的SQL
    pub fn shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM jineng_huizong WHERE jineng_id = ?", ziduan_str)
    }

    // ==================== 复杂查询SQL ====================

    /// 获取技能总数的SQL
    pub const sql_huoqu_jineng_zongshu: &'static str =
        "SELECT COUNT(*) as total FROM jineng_huizong";

    /// 获取有基础信息的技能数量SQL
    pub const sql_huoqu_you_jichuxinxi_jineng_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE you_jichuxinxi = 'true'";

    /// 获取有物品信息的技能数量SQL
    pub const sql_huoqu_you_wupin_jineng_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE you_wupin = 'true'";

    /// 获取有职业信息的技能数量SQL
    pub const sql_huoqu_you_zhiye_jineng_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE you_zhiye = 'true'";

    /// 获取有历史变动信息的技能数量SQL
    pub const sql_huoqu_you_lishibiandong_jineng_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE you_lishibiandong = 'true'";

    // ==================== 分页查询SQL ====================

    /// 生成分页查询技能列表的SQL
    pub fn shengcheng_sql_fenye_chaxun_jineng_liebiao(limit: u32, offset: u32) -> String {
        format!(
            "SELECT jineng_id, jineng_mingcheng, zhongwenming, leiming 
             FROM jineng_huizong 
             ORDER BY jineng_id 
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 生成按条件搜索技能的SQL
    pub fn shengcheng_sql_sousuo_jineng(sousuo_guanjianci: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT jineng_id, jineng_mingcheng, zhongwenming, leiming 
             FROM jineng_huizong 
             WHERE jineng_mingcheng LIKE '%{}%' 
                OR zhongwenming LIKE '%{}%' 
                OR leiming LIKE '%{}%'
             ORDER BY jineng_id 
             LIMIT {} OFFSET {}",
            sousuo_guanjianci, sousuo_guanjianci, sousuo_guanjianci, limit, offset
        )
    }

    /// 生成按类型筛选技能的SQL
    pub fn shengcheng_sql_anzhiye_shaixuan_jineng(zhiye_mingcheng: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT jineng_id, jineng_mingcheng, zhongwenming, leiming
             FROM jineng_huizong
             WHERE zhiye_yaml LIKE '%{}%'
             ORDER BY jineng_id
             LIMIT {} OFFSET {}",
            zhiye_mingcheng, limit, offset
        )
    }

    // ==================== 技能列表查询SQL ====================

    /// 获取技能列表的SQL（分页查询）
    pub fn shengcheng_sql_jineng_liebiao_fenye(limit: u32, offset: u32) -> String {
        format!(
            "SELECT jineng_id, jineng_mingcheng, leiming
             FROM jineng_huizong
             WHERE jineng_id IS NOT NULL
             ORDER BY jineng_id
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 批量查询技能名称的SQL
    pub fn shengcheng_sql_piliang_chaxun_jineng_mingcheng(id_liebiao: &[i32]) -> String {
        let id_str = id_liebiao.iter()
            .map(|id| id.to_string())
            .collect::<Vec<String>>()
            .join(",");
        format!(
            "SELECT ID, schinese FROM skill_name WHERE ID IN ({})",
            id_str
        )
    }

    // ==================== 技能名字查询SQL ====================

    /// 根据类名精确查询技能总数的SQL
    pub const sql_chaxun_by_leiming_jingque_zongshu: &'static str =
        "SELECT COUNT(*) as count FROM jineng_huizong WHERE leiming = ?";

    /// 根据类名精确查询技能列表的SQL
    pub const sql_chaxun_by_leiming_jingque: &'static str =
        "SELECT jineng_id, jineng_mingcheng, leiming FROM jineng_huizong WHERE leiming = ? ORDER BY jineng_id";

    /// 根据名字精确查询技能总数的SQL（同时查询skill_name和jineng_huizong表）
    pub const sql_chaxun_by_mingzi_jingque_zongshu: &'static str =
        "SELECT COUNT(DISTINCT h.jineng_id) as count
         FROM jineng_huizong h
         LEFT JOIN skill_name s ON h.jineng_id = s.ID
         WHERE h.jineng_mingcheng = ? OR s.schinese = ?";

    /// 根据名字精确查询技能列表的SQL（同时查询skill_name和jineng_huizong表）
    pub const sql_chaxun_by_mingzi_jingque: &'static str =
        "SELECT DISTINCT h.jineng_id,
                COALESCE(s.schinese, h.jineng_mingcheng) as jineng_mingcheng,
                h.leiming
         FROM jineng_huizong h
         LEFT JOIN skill_name s ON h.jineng_id = s.ID
         WHERE h.jineng_mingcheng = ? OR s.schinese = ?
         ORDER BY h.jineng_id";

    /// 根据名字模糊查询技能总数的SQL（同时查询skill_name和jineng_huizong表）
    pub const sql_chaxun_by_mingzi_mohu_zongshu: &'static str =
        "SELECT COUNT(DISTINCT h.jineng_id) as count
         FROM jineng_huizong h
         LEFT JOIN skill_name s ON h.jineng_id = s.ID
         WHERE h.jineng_mingcheng LIKE ? OR s.schinese LIKE ?";

    /// 根据名字模糊查询技能列表的SQL（同时查询skill_name和jineng_huizong表）
    pub const sql_chaxun_by_mingzi_mohu: &'static str =
        "SELECT DISTINCT h.jineng_id,
                COALESCE(s.schinese, h.jineng_mingcheng) as jineng_mingcheng,
                h.leiming
         FROM jineng_huizong h
         LEFT JOIN skill_name s ON h.jineng_id = s.ID
         WHERE h.jineng_mingcheng LIKE ? OR s.schinese LIKE ?
         ORDER BY h.jineng_id";
}
