#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_redis_kongzhi::jineng_redis_kongzhi;
use super::jineng_rizhi_kongzhi::jineng_zifuchuan_changliangguanli;
use super::jineng_sql_kongzhi::jineng_sql_guanli;
use super::jinengshujujiegouti::{jineng_liebiao_fenye_canshu, jineng_liebiao_jieguo, jineng_liebiao_xiang, jineng_liebiao_fenye_xinxi};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde::{Deserialize, Serialize};
use sqlx::Row;

/// 名字查询类型枚举
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub enum jineng_mingzi_chaxun_leixing {
    /// 类名精确查询
    leiming_jingque,
    /// 名字精确查询
    mingzi_jingque,
    /// 名字模糊查询
    mingzi_mohu,
}

impl jineng_mingzi_chaxun_leixing {
    /// 转换为字符串标识
    pub fn to_string(&self) -> String {
        match self {
            Self::leiming_jingque => "leiming_jingque".to_string(),
            Self::mingzi_jingque => "mingzi_jingque".to_string(),
            Self::mingzi_mohu => "mingzi_mohu".to_string(),
        }
    }
}

/// 名字查询条件结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_mingzi_chaxun_tiaojian {
    /// 查询类型
    pub chaxun_leixing: jineng_mingzi_chaxun_leixing,
    /// 查询值
    pub chaxun_zhi: String,
    /// 分页参数
    pub fenye_canshu: jineng_liebiao_fenye_canshu,
}

impl jineng_mingzi_chaxun_tiaojian {
    /// 创建类名精确查询条件
    pub fn leiming_jingque(leiming: String, fenye_canshu: jineng_liebiao_fenye_canshu) -> Self {
        Self {
            chaxun_leixing: jineng_mingzi_chaxun_leixing::leiming_jingque,
            chaxun_zhi: leiming,
            fenye_canshu,
        }
    }

    /// 创建名字精确查询条件
    pub fn mingzi_jingque(mingzi: String, fenye_canshu: jineng_liebiao_fenye_canshu) -> Self {
        Self {
            chaxun_leixing: jineng_mingzi_chaxun_leixing::mingzi_jingque,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }

    /// 创建名字模糊查询条件
    pub fn mingzi_mohu(mingzi: String, fenye_canshu: jineng_liebiao_fenye_canshu) -> Self {
        Self {
            chaxun_leixing: jineng_mingzi_chaxun_leixing::mingzi_mohu,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }
}

/// 技能名字查询管理器
pub struct jineng_mingzi_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl jineng_mingzi_chaxun_guanli {
    /// 创建新的技能名字查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的技能名字查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 统一名字查询入口
    pub async fn tongyong_mingzi_chaxun(&self, chaxun_tiaojian: &jineng_mingzi_chaxun_tiaojian) -> anyhow::Result<jineng_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = jineng_zifuchuan_changliangguanli::shengcheng_redis_jian_mingzi_chaxun(
                &chaxun_tiaojian.chaxun_leixing.to_string(),
                &chaxun_tiaojian.chaxun_zhi,
                chaxun_tiaojian.fenye_canshu.dangqian_ye,
                chaxun_tiaojian.fenye_canshu.meiye_shuliang,
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(mut liebiao_jieguo) = serde_json::from_str::<jineng_liebiao_jieguo>(&huancun_shuju) {
                    // 设置数据来源为Redis
                    liebiao_jieguo.laiyuan = Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_redis.to_string());
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        let liebiao_jieguo = match chaxun_tiaojian.chaxun_leixing {
            jineng_mingzi_chaxun_leixing::leiming_jingque => {
                self.chaxun_by_leiming_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            jineng_mingzi_chaxun_leixing::mingzi_jingque => {
                self.chaxun_by_mingzi_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            jineng_mingzi_chaxun_leixing::mingzi_mohu => {
                self.chaxun_by_mingzi_mohu(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
        };

        match liebiao_jieguo {
            Ok(jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = jineng_zifuchuan_changliangguanli::shengcheng_redis_jian_mingzi_chaxun(
                        &chaxun_tiaojian.chaxun_leixing.to_string(),
                        &chaxun_tiaojian.chaxun_zhi,
                        chaxun_tiaojian.fenye_canshu.dangqian_ye,
                        chaxun_tiaojian.fenye_canshu.meiye_shuliang,
                    );

                    if let Ok(json_shuju) = serde_json::to_string(&jieguo) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, jineng_zifuchuan_changliangguanli::mingzi_chaxun_huancun_shijian as i64).await;
                    }
                }

                Ok(jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = format!("技能名字查询失败: {}", e);
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(jineng_liebiao_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(cuowu_xinxi),
                    laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                    jineng_liebiao: Vec::new(),
                    fenye_xinxi: jineng_liebiao_fenye_xinxi {
                        dangqian_ye: chaxun_tiaojian.fenye_canshu.dangqian_ye,
                        zongyeshu: 0,
                        zong_jineng_shu: 0,
                        dangqianye_jineng_shu: 0,
                    },
                })
            }
        }
    }

    /// 根据类名精确查询技能列表
    pub async fn chaxun_by_leiming_jingque(&self, leiming: &str, fenye_canshu: &jineng_liebiao_fenye_canshu) -> anyhow::Result<jineng_liebiao_jieguo> {
        // 获取总数
        let zongshu_query = sqlx::query(jineng_sql_guanli::sql_chaxun_by_leiming_jingque_zongshu)
            .bind(leiming);
        let zongshu_row = zongshu_query
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;
        let zong_jineng_shu: i64 = zongshu_row.get("count");

        // 计算分页信息
        let zongyeshu = if zong_jineng_shu == 0 {
            0
        } else {
            ((zong_jineng_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查当前页是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(jineng_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(fenye_canshu.dangqian_ye, zongyeshu)),
                laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                jineng_liebiao: Vec::new(),
                fenye_xinxi: jineng_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_jineng_shu: zong_jineng_shu as u64,
                    dangqianye_jineng_shu: 0,
                },
            });
        }

        // 计算偏移量
        let offset = (fenye_canshu.dangqian_ye - 1) * fenye_canshu.meiye_shuliang;

        // 查询技能列表
        let liebiao_sql_with_limit = format!("{} LIMIT {} OFFSET {}", jineng_sql_guanli::sql_chaxun_by_leiming_jingque, fenye_canshu.meiye_shuliang, offset);
        let liebiao_query = sqlx::query(&liebiao_sql_with_limit)
            .bind(leiming);
        let rows = liebiao_query
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut jineng_liebiao = Vec::new();
        for row in rows {
            let jineng_id: Option<i32> = row.try_get("jineng_id").unwrap_or(None);
            let jineng_mingcheng: Option<String> = row.try_get("jineng_mingcheng").unwrap_or(None);
            let leiming: Option<String> = row.try_get("leiming").unwrap_or(None);

            if let Some(id) = jineng_id {
                jineng_liebiao.push(jineng_liebiao_xiang {
                    jineng_id: id,
                    jineng_mingcheng: jineng_mingcheng.unwrap_or_default(),
                    leiming,
                });
            }
        }

        let dangqianye_jineng_shu = jineng_liebiao.len() as u32;

        Ok(jineng_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
            jineng_liebiao,
            fenye_xinxi: jineng_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_jineng_shu: zong_jineng_shu as u64,
                dangqianye_jineng_shu,
            },
        })
    }

    /// 根据名字精确查询技能列表
    pub async fn chaxun_by_mingzi_jingque(&self, mingzi: &str, fenye_canshu: &jineng_liebiao_fenye_canshu) -> anyhow::Result<jineng_liebiao_jieguo> {
        // 获取总数
        let zongshu_query = sqlx::query(jineng_sql_guanli::sql_chaxun_by_mingzi_jingque_zongshu)
            .bind(mingzi)
            .bind(mingzi);
        let zongshu_row = zongshu_query
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;
        let zong_jineng_shu: i64 = zongshu_row.get("count");

        // 计算分页信息
        let zongyeshu = if zong_jineng_shu == 0 {
            0
        } else {
            ((zong_jineng_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查当前页是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(jineng_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(fenye_canshu.dangqian_ye, zongyeshu)),
                laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                jineng_liebiao: Vec::new(),
                fenye_xinxi: jineng_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_jineng_shu: zong_jineng_shu as u64,
                    dangqianye_jineng_shu: 0,
                },
            });
        }

        // 计算偏移量
        let offset = (fenye_canshu.dangqian_ye - 1) * fenye_canshu.meiye_shuliang;

        // 查询技能列表
        let liebiao_sql_with_limit = format!("{} LIMIT {} OFFSET {}", jineng_sql_guanli::sql_chaxun_by_mingzi_jingque, fenye_canshu.meiye_shuliang, offset);
        let liebiao_query = sqlx::query(&liebiao_sql_with_limit)
            .bind(mingzi)
            .bind(mingzi);
        let rows = liebiao_query
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut jineng_liebiao = Vec::new();
        for row in rows {
            let jineng_id: Option<i32> = row.try_get("jineng_id").unwrap_or(None);
            let jineng_mingcheng: Option<String> = row.try_get("jineng_mingcheng").unwrap_or(None);
            let leiming: Option<String> = row.try_get("leiming").unwrap_or(None);

            if let Some(id) = jineng_id {
                jineng_liebiao.push(jineng_liebiao_xiang {
                    jineng_id: id,
                    jineng_mingcheng: jineng_mingcheng.unwrap_or_default(),
                    leiming,
                });
            }
        }

        let dangqianye_jineng_shu = jineng_liebiao.len() as u32;

        Ok(jineng_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
            jineng_liebiao,
            fenye_xinxi: jineng_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_jineng_shu: zong_jineng_shu as u64,
                dangqianye_jineng_shu,
            },
        })
    }

    /// 根据名字模糊查询技能列表
    pub async fn chaxun_by_mingzi_mohu(&self, mingzi: &str, fenye_canshu: &jineng_liebiao_fenye_canshu) -> anyhow::Result<jineng_liebiao_jieguo> {
        let mohu_mingzi = format!("%{}%", mingzi);

        // 获取总数
        let zongshu_query = sqlx::query(jineng_sql_guanli::sql_chaxun_by_mingzi_mohu_zongshu)
            .bind(&mohu_mingzi)
            .bind(&mohu_mingzi);
        let zongshu_row = zongshu_query
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;
        let zong_jineng_shu: i64 = zongshu_row.get("count");

        // 计算分页信息
        let zongyeshu = if zong_jineng_shu == 0 {
            0
        } else {
            ((zong_jineng_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查当前页是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(jineng_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(fenye_canshu.dangqian_ye, zongyeshu)),
                laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
                jineng_liebiao: Vec::new(),
                fenye_xinxi: jineng_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_jineng_shu: zong_jineng_shu as u64,
                    dangqianye_jineng_shu: 0,
                },
            });
        }

        // 计算偏移量
        let offset = (fenye_canshu.dangqian_ye - 1) * fenye_canshu.meiye_shuliang;

        // 查询技能列表
        let liebiao_sql_with_limit = format!("{} LIMIT {} OFFSET {}", jineng_sql_guanli::sql_chaxun_by_mingzi_mohu, fenye_canshu.meiye_shuliang, offset);
        let liebiao_query = sqlx::query(&liebiao_sql_with_limit)
            .bind(&mohu_mingzi)
            .bind(&mohu_mingzi);
        let rows = liebiao_query
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut jineng_liebiao = Vec::new();
        for row in rows {
            let jineng_id: Option<i32> = row.try_get("jineng_id").unwrap_or(None);
            let jineng_mingcheng: Option<String> = row.try_get("jineng_mingcheng").unwrap_or(None);
            let leiming: Option<String> = row.try_get("leiming").unwrap_or(None);

            if let Some(id) = jineng_id {
                jineng_liebiao.push(jineng_liebiao_xiang {
                    jineng_id: id,
                    jineng_mingcheng: jineng_mingcheng.unwrap_or_default(),
                    leiming,
                });
            }
        }

        let dangqianye_jineng_shu = jineng_liebiao.len() as u32;

        Ok(jineng_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            laiyuan: Some(jineng_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string()),
            jineng_liebiao,
            fenye_xinxi: jineng_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_jineng_shu: zong_jineng_shu as u64,
                dangqianye_jineng_shu,
            },
        })
    }

    /// 清除技能名字查询缓存
    pub async fn qingchu_mingzi_chaxun_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let moshi = jineng_zifuchuan_changliangguanli::redis_jian_moshi_jineng_mingzi_chaxun;
            redis.shanchu_by_pattern(moshi).await?;
        }
        Ok(())
    }

}
