#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// skill_name表基础信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_jiben_xinxi {
    /// 技能ID
    pub id: Option<i32>,
    /// Aegis名称
    pub aegis_name: Option<String>,
    /// 英文名称
    pub name: Option<String>,
    /// 简体中文名称
    pub schinese: Option<String>,
    /// 繁体中文名称
    pub tchinese: Option<String>,
    /// 日文名称
    pub jp: Option<String>,
    /// 韩文名称
    pub kr: Option<String>,
}

/// jineng_huizong表汇总信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_huizong_xinxi {
    /// 技能ID
    pub jineng_id: Option<i32>,
    /// 技能名称
    pub jineng_mingcheng: Option<String>,
    /// 物品数量
    pub wupin_shuliang: Option<i32>,
    /// 职业数量
    pub zhiye_shuliang: Option<i32>,
    /// 历史变动数量
    pub lishibiandong_shuliang: Option<i32>,
    /// 中文名
    pub zhongwenming: Option<String>,
    /// 类名
    pub leiming: Option<String>,
    /// 图片URL
    pub tupian_url: Option<String>,
    /// 最大等级
    pub zuida_dengji: Option<i32>,
    /// 基础信息YAML
    pub jichuxinxi_yaml: Option<String>,
    /// 物品YAML
    pub wupin_yaml: Option<String>,
    /// 职业YAML
    pub zhiye_yaml: Option<String>,
    /// 历史变动YAML
    pub lishibiandong_yaml: Option<String>,
    /// 有基础信息
    pub you_jichuxinxi: Option<String>,
    /// 有物品
    pub you_wupin: Option<String>,
    /// 有职业
    pub you_zhiye: Option<String>,
    /// 有历史变动
    pub you_lishibiandong: Option<String>,
}

/// 完整技能信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_wanzheng_xinxi {
    /// 技能ID
    pub id: String,
    /// 基础信息（来自skill_name表）
    pub jiben_xinxi: Option<jineng_jiben_xinxi>,
    /// 汇总信息（来自jineng_huizong表）
    pub huizong_xinxi: Option<jineng_huizong_xinxi>,
}

/// 技能查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 数据来源（mysql/redis）
    pub laiyuan: String,
    /// 完整技能信息（全部信息查询时使用）
    pub wanzheng_xinxi: Option<jineng_wanzheng_xinxi>,
    /// 技能数据（指定字段查询时使用）
    pub jineng_shuju: Option<HashMap<String, String>>,
}

/// 技能列表项结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_liebiao_xiang {
    /// 技能ID
    pub jineng_id: i32,
    /// 技能名称（优先使用name表的schinese，如果没有则使用huizong表的jineng_mingcheng）
    pub jineng_mingcheng: String,
    /// 类名
    pub leiming: Option<String>,
}

/// 分页参数结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_liebiao_fenye_canshu {
    /// 每页技能数量
    pub meiye_shuliang: u32,
    /// 当前页数（从1开始）
    pub dangqian_ye: u32,
}

/// 分页信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_liebiao_fenye_xinxi {
    /// 当前页数
    pub dangqian_ye: u32,
    /// 总页数
    pub zongyeshu: u32,
    /// 总技能数
    pub zong_jineng_shu: u64,
    /// 当前页技能数
    pub dangqianye_jineng_shu: u32,
}

/// 技能列表查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jineng_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 数据来源（mysql/redis）
    pub laiyuan: Option<String>,
    /// 技能列表
    pub jineng_liebiao: Vec<jineng_liebiao_xiang>,
    /// 分页信息
    pub fenye_xinxi: jineng_liebiao_fenye_xinxi,
}

/// 技能字段映射管理类
pub struct jineng_ziduan_yingshe;

impl jineng_ziduan_yingshe {
    /// 获取skill_name表的所有字段
    pub fn huoqu_jiben_biao_ziduan() -> Vec<String> {
        vec![
            "ID".to_string(),
            "Aegis_name".to_string(),
            "name".to_string(),
            "schinese".to_string(),
            "tchinese".to_string(),
            "jp".to_string(),
            "kr".to_string(),
        ]
    }

    /// 获取jineng_huizong表的所有字段
    pub fn huoqu_huizong_biao_ziduan() -> Vec<String> {
        vec![
            "jineng_id".to_string(),
            "jineng_mingcheng".to_string(),
            "wupin_shuliang".to_string(),
            "zhiye_shuliang".to_string(),
            "lishibiandong_shuliang".to_string(),
            "zhongwenming".to_string(),
            "leiming".to_string(),
            "tupian_url".to_string(),
            "zuida_dengji".to_string(),
            "jichuxinxi_yaml".to_string(),
            "wupin_yaml".to_string(),
            "zhiye_yaml".to_string(),
            "lishibiandong_yaml".to_string(),
            "you_jichuxinxi".to_string(),
            "you_wupin".to_string(),
            "you_zhiye".to_string(),
            "you_lishibiandong".to_string(),
        ]
    }

    /// 检查字段是否在汇总表中
    pub fn jiancha_ziduan_zai_huizong_biao(ziduan_ming: &str) -> bool {
        let huizong_ziduan = Self::huoqu_huizong_biao_ziduan();
        huizong_ziduan.contains(&ziduan_ming.to_string())
    }

    /// 检查字段是否在基础表中
    pub fn jiancha_ziduan_zai_jiben_biao(ziduan_ming: &str) -> bool {
        let jiben_ziduan = Self::huoqu_jiben_biao_ziduan();
        jiben_ziduan.contains(&ziduan_ming.to_string())
    }

    /// 检查字段是否有效（在任一表中）
    pub fn jiancha_ziduan_youxiao(ziduan_ming: &str) -> bool {
        Self::jiancha_ziduan_zai_jiben_biao(ziduan_ming) || Self::jiancha_ziduan_zai_huizong_biao(ziduan_ming)
    }
}
