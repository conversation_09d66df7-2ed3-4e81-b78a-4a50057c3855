#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::wupin_mingzi_chaxun::{mingzi_chaxun_leixing, mingzi_chaxun_tiaojian};
use super::wupin_redis_kongzhi::wupin_redis_kongzhi;
use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use super::wupinjiegouti::{fenye_canshu, wupin_liebiao_jieguo, wupin_liebiao_xiang};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde::{Deserialize, Serialize};
use sqlx::Row;

/// 分类列表查询条件结构体
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct fenlei_liebiao_chaxun_tiaojian {
    /// 类型（可选）
    pub leixing: Option<String>,
    /// 子类型（可选）
    pub zileixing: Option<String>,
    /// 名字查询条件（可选）
    pub mingzi_tiaojian: Option<mingzi_chaxun_tiaojian>,
    /// 分页参数
    pub fenye_canshu: fenye_canshu,
}

impl fenlei_liebiao_chaxun_tiaojian {
    /// 创建仅按类型查询的条件
    pub fn leixing_chaxun(leixing: String, fenye_canshu: fenye_canshu) -> Self {
        Self {
            leixing: Some(leixing),
            zileixing: None,
            mingzi_tiaojian: None,
            fenye_canshu,
        }
    }

    /// 创建类型+子类型查询的条件
    pub fn leixing_zileixing_chaxun(leixing: String, zileixing: String, fenye_canshu: fenye_canshu) -> Self {
        Self {
            leixing: Some(leixing),
            zileixing: Some(zileixing),
            mingzi_tiaojian: None,
            fenye_canshu,
        }
    }

    /// 创建类型+名字查询的条件
    pub fn leixing_mingzi_chaxun(leixing: String, mingzi_tiaojian: mingzi_chaxun_tiaojian, fenye_canshu: fenye_canshu) -> Self {
        Self {
            leixing: Some(leixing),
            zileixing: None,
            mingzi_tiaojian: Some(mingzi_tiaojian),
            fenye_canshu,
        }
    }

    /// 创建类型+子类型+名字查询的条件
    pub fn quanbu_lianhe_chaxun(leixing: String, zileixing: String, mingzi_tiaojian: mingzi_chaxun_tiaojian, fenye_canshu: fenye_canshu) -> Self {
        Self {
            leixing: Some(leixing),
            zileixing: Some(zileixing),
            mingzi_tiaojian: Some(mingzi_tiaojian),
            fenye_canshu,
        }
    }

    /// 创建仅按名字查询的条件
    pub fn mingzi_chaxun(mingzi_tiaojian: mingzi_chaxun_tiaojian, fenye_canshu: fenye_canshu) -> Self {
        Self {
            leixing: None,
            zileixing: None,
            mingzi_tiaojian: Some(mingzi_tiaojian),
            fenye_canshu,
        }
    }

    /// 生成缓存键标识
    pub fn shengcheng_huancun_jian_biaoshi(&self) -> String {
        let leixing_str = self.leixing.as_deref().unwrap_or("null");
        let zileixing_str = self.zileixing.as_deref().unwrap_or("null");
        let mingzi_str = if let Some(ref mingzi_tiaojian) = self.mingzi_tiaojian {
            format!("{}_{}", mingzi_tiaojian.chaxun_leixing.to_string(), mingzi_tiaojian.chaxun_zhi)
        } else {
            "null".to_string()
        };

        format!("{}:{}:{}:{}:{}",
                leixing_str,
                zileixing_str,
                mingzi_str,
                self.fenye_canshu.yema,
                self.fenye_canshu.meiye_shuliang
        )
    }
}

/// 物品分类列表查询管理器
pub struct wupin_fenlei_liebiao_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl wupin_fenlei_liebiao_chaxun_guanli {
    /// 创建新的分类列表查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的分类列表查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 统一分类列表查询入口
    pub async fn tongyong_fenlei_liebiao_chaxun(&self, chaxun_tiaojian: &fenlei_liebiao_chaxun_tiaojian) -> anyhow::Result<wupin_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_liebiao(&chaxun_tiaojian.shengcheng_huancun_jian_biaoshi());

            let xinxi = wupin_zifuchuan_changliangguanli::shengcheng_xinxi_changshi_redis_huoqu_huancun(&huancun_jian);
            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                &xinxi,
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );

            match redis.huoqu(&huancun_jian).await {
                Ok(Some(huancun_shuju)) => {
                    match serde_json::from_str::<wupin_liebiao_jieguo>(&huancun_shuju) {
                        Ok(mut liebiao_jieguo) => {
                            liebiao_jieguo.shuju_laiyuan = Some(wupin_zifuchuan_changliangguanli::shuju_laiyuan_redis.to_string());
                            let xinxi = wupin_zifuchuan_changliangguanli::shengcheng_xinxi_redis_huoqu_fenlei_liebiao_jieguo(liebiao_jieguo.zong_shuliang);
                            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                                &xinxi,
                                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                            );
                            return Ok(liebiao_jieguo);
                        }
                        Err(e) => {
                            let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_fanxuliehua_redis_huancun_shibai(&e.to_string());
                            crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                                &cuowu_xinxi,
                                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                            );
                        }
                    }
                }
                Ok(None) => {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        wupin_zifuchuan_changliangguanli::xinxi_redis_wei_zhaodao_huancun,
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                }
                Err(e) => {
                    let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_redis_huoqu_huancun_shibai(&e.to_string());
                    crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                        &cuowu_xinxi,
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                }
            }
        } else {
            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                wupin_zifuchuan_changliangguanli::xinxi_redis_lianjie_wei_chushihua,
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );
        }

        // 从数据库查询
        let liebiao_jieguo = self.zhixing_shujuku_chaxun(chaxun_tiaojian).await;

        match liebiao_jieguo {
            Ok(jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_liebiao(&chaxun_tiaojian.shengcheng_huancun_jian_biaoshi());

                    match serde_json::to_string(&jieguo) {
                        Ok(json_shuju) => {
                            match redis.shezhi_with_guoqi(&huancun_jian, &json_shuju, wupin_zifuchuan_changliangguanli::fenlei_liebiao_huancun_shijian as i64).await {
                                Ok(_) => {
                                    let xinxi = wupin_zifuchuan_changliangguanli::shengcheng_xinxi_fenlei_liebiao_yi_huancun_redis(&huancun_jian);
                                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                                        &xinxi,
                                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                                    );
                                }
                                Err(e) => {
                                    let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_redis_huancun_shibai(&e.to_string());
                                    crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                                        &cuowu_xinxi,
                                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                                    );
                                }
                            }
                        }
                        Err(e) => {
                            let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_xuliehua_shibai(&e.to_string());
                            crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                                &cuowu_xinxi,
                                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                            );
                        }
                    }
                }

                Ok(jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_fenlei_liebiao_chaxun_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(wupin_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 执行数据库查询
    async fn zhixing_shujuku_chaxun(&self, chaxun_tiaojian: &fenlei_liebiao_chaxun_tiaojian) -> anyhow::Result<wupin_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_zongshu(chaxun_tiaojian).await?;

        // 构建查询SQL
        let (sql, bind_values) = self.gouzao_chaxun_sql(chaxun_tiaojian);

        // 执行查询
        let mut query = sqlx::query(&sql);
        for value in bind_values {
            query = query.bind(value);
        }

        match query.fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?).await {
            Ok(rows) => {
                let wupin_liebiao = self.jiexi_chaxun_jieguo(rows)?;
                Ok(wupin_liebiao_jieguo::chenggong(wupin_liebiao, &chaxun_tiaojian.fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_liebiao_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询总数
    async fn chaxun_zongshu(&self, chaxun_tiaojian: &fenlei_liebiao_chaxun_tiaojian) -> anyhow::Result<u64> {
        let (sql, bind_values) = self.gouzao_zongshu_sql(chaxun_tiaojian);

        let mut query = sqlx::query(&sql);
        for value in bind_values {
            query = query.bind(value);
        }

        match query.fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?).await {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 构建查询SQL
    fn gouzao_chaxun_sql(&self, chaxun_tiaojian: &fenlei_liebiao_chaxun_tiaojian) -> (String, Vec<String>) {
        let mut sql = r#"
            SELECT DISTINCT
                h.wupin_id,
                COALESCE(n.zhognwenming, h.zhongwenming, CONCAT('物品_', h.wupin_id)) as wupin_mingcheng,
                h.leixing,
                h.zileixing,
                h.leiming
            FROM wupin_huizong h
            LEFT JOIN item_name n ON h.wupin_id = n.ID
            WHERE h.wupin_id IS NOT NULL AND h.wupin_id != ''
        "#.to_string();

        let mut bind_values = Vec::new();

        // 添加类型条件
        if let Some(ref leixing) = chaxun_tiaojian.leixing {
            sql.push_str(" AND h.leixing = ?");
            bind_values.push(leixing.clone());
        }

        // 添加子类型条件
        if let Some(ref zileixing) = chaxun_tiaojian.zileixing {
            sql.push_str(" AND h.zileixing = ?");
            bind_values.push(zileixing.clone());
        }

        // 添加名字查询条件
        if let Some(ref mingzi_tiaojian) = chaxun_tiaojian.mingzi_tiaojian {
            match mingzi_tiaojian.chaxun_leixing {
                mingzi_chaxun_leixing::leiming_jingque => {
                    sql.push_str(" AND h.leiming = ?");
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                }
                mingzi_chaxun_leixing::mingzi_jingque => {
                    sql.push_str(" AND (n.zhognwenming = ? OR h.zhongwenming = ?)");
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                }
                mingzi_chaxun_leixing::mingzi_mohu => {
                    let mohu_moshi = format!("%{}%", mingzi_tiaojian.chaxun_zhi);
                    sql.push_str(" AND (n.zhognwenming LIKE ? OR h.zhongwenming LIKE ?)");
                    bind_values.push(mohu_moshi.clone());
                    bind_values.push(mohu_moshi);
                }
            }
        }

        // 添加排序和分页
        sql.push_str(" ORDER BY h.wupin_id LIMIT ? OFFSET ?");
        bind_values.push(chaxun_tiaojian.fenye_canshu.meiye_shuliang.to_string());
        bind_values.push(chaxun_tiaojian.fenye_canshu.jisuan_pianyi().to_string());

        (sql, bind_values)
    }

    /// 构建总数查询SQL
    fn gouzao_zongshu_sql(&self, chaxun_tiaojian: &fenlei_liebiao_chaxun_tiaojian) -> (String, Vec<String>) {
        let mut sql = r#"
            SELECT COUNT(DISTINCT h.wupin_id) as zong_shuliang
            FROM wupin_huizong h
            LEFT JOIN item_name n ON h.wupin_id = n.ID
            WHERE h.wupin_id IS NOT NULL AND h.wupin_id != ''
        "#.to_string();

        let mut bind_values = Vec::new();

        // 添加类型条件
        if let Some(ref leixing) = chaxun_tiaojian.leixing {
            sql.push_str(" AND h.leixing = ?");
            bind_values.push(leixing.clone());
        }

        // 添加子类型条件
        if let Some(ref zileixing) = chaxun_tiaojian.zileixing {
            sql.push_str(" AND h.zileixing = ?");
            bind_values.push(zileixing.clone());
        }

        // 添加名字查询条件
        if let Some(ref mingzi_tiaojian) = chaxun_tiaojian.mingzi_tiaojian {
            match mingzi_tiaojian.chaxun_leixing {
                mingzi_chaxun_leixing::leiming_jingque => {
                    sql.push_str(" AND h.leiming = ?");
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                }
                mingzi_chaxun_leixing::mingzi_jingque => {
                    sql.push_str(" AND (n.zhognwenming = ? OR h.zhongwenming = ?)");
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                    bind_values.push(mingzi_tiaojian.chaxun_zhi.clone());
                }
                mingzi_chaxun_leixing::mingzi_mohu => {
                    let mohu_moshi = format!("%{}%", mingzi_tiaojian.chaxun_zhi);
                    sql.push_str(" AND (n.zhognwenming LIKE ? OR h.zhongwenming LIKE ?)");
                    bind_values.push(mohu_moshi.clone());
                    bind_values.push(mohu_moshi);
                }
            }
        }

        (sql, bind_values)
    }

    /// 解析查询结果行数据
    fn jiexi_chaxun_jieguo(&self, rows: Vec<sqlx::mysql::MySqlRow>) -> anyhow::Result<Vec<wupin_liebiao_xiang>> {
        let mut wupin_liebiao = Vec::new();

        for row in rows {
            // 尝试获取物品ID，如果失败则跳过这行
            if let Ok(wupin_id) = row.try_get::<i32, _>("wupin_id") {
                // 获取物品名称，如果为NULL则使用默认值
                let wupin_mingcheng = row.try_get::<Option<String>, _>("wupin_mingcheng")
                    .unwrap_or(None)
                    .unwrap_or_else(|| format!("物品_{}", wupin_id));

                // 获取其他可选字段
                let leixing = row.try_get::<Option<String>, _>("leixing").unwrap_or(None);
                let zileixing = row.try_get::<Option<String>, _>("zileixing").unwrap_or(None);
                let leiming = row.try_get::<Option<String>, _>("leiming").unwrap_or(None);

                wupin_liebiao.push(wupin_liebiao_xiang {
                    wupin_id,
                    wupin_mingcheng,
                    leixing,
                    zileixing,
                    leiming,
                });
            }
        }

        Ok(wupin_liebiao)
    }

    /// 按类型查询物品列表
    pub async fn chaxun_by_leixing(&self, leixing: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::leixing_chaxun(
            leixing.to_string(),
            fenye_canshu.clone(),
        );
        self.tongyong_fenlei_liebiao_chaxun(&chaxun_tiaojian).await
    }

    /// 按类型和子类型查询物品列表
    pub async fn chaxun_by_leixing_zileixing(&self, leixing: &str, zileixing: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::leixing_zileixing_chaxun(
            leixing.to_string(),
            zileixing.to_string(),
            fenye_canshu.clone(),
        );
        self.tongyong_fenlei_liebiao_chaxun(&chaxun_tiaojian).await
    }

    /// 按类型和名字查询物品列表
    pub async fn chaxun_by_leixing_mingzi(&self, leixing: &str, mingzi_tiaojian: &mingzi_chaxun_tiaojian, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::leixing_mingzi_chaxun(
            leixing.to_string(),
            mingzi_tiaojian.clone(),
            fenye_canshu.clone(),
        );
        self.tongyong_fenlei_liebiao_chaxun(&chaxun_tiaojian).await
    }

    /// 联合查询（类型+子类型+名字）
    pub async fn lianhe_chaxun(&self, leixing: &str, zileixing: &str, mingzi_tiaojian: &mingzi_chaxun_tiaojian, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::quanbu_lianhe_chaxun(
            leixing.to_string(),
            zileixing.to_string(),
            mingzi_tiaojian.clone(),
            fenye_canshu.clone(),
        );
        self.tongyong_fenlei_liebiao_chaxun(&chaxun_tiaojian).await
    }

    /// 获取分类列表查询缓存统计信息
    pub async fn huoqu_fenlei_liebiao_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis.clone());
            redis_kongzhi.huoqu_fenlei_liebiao_huancun_tongji().await
        } else {
            Ok(wupin_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }

    /// 清除分类列表查询缓存
    pub async fn qingchu_fenlei_liebiao_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis.clone());
            redis_kongzhi.qingchu_fenlei_liebiao_huancun().await
        } else {
            Ok(())
        }
    }

    /// 按子类型查询物品列表（不限制类型）
    pub async fn chaxun_by_zileixing(&self, zileixing: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian {
            leixing: None,
            zileixing: Some(zileixing.to_string()),
            mingzi_tiaojian: None,
            fenye_canshu: fenye_canshu.clone(),
        };
        self.tongyong_fenlei_liebiao_chaxun(&chaxun_tiaojian).await
    }

    /// 获取指定类型下的物品统计信息
    pub async fn huoqu_leixing_tongji(&self, leixing: &str) -> anyhow::Result<u64> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::leixing_chaxun(
            leixing.to_string(),
            fenye_canshu::new(1, 1), // 只需要总数，不需要实际数据
        );
        self.chaxun_zongshu(&chaxun_tiaojian).await
    }

    /// 获取指定类型和子类型下的物品统计信息
    pub async fn huoqu_leixing_zileixing_tongji(&self, leixing: &str, zileixing: &str) -> anyhow::Result<u64> {
        let chaxun_tiaojian = fenlei_liebiao_chaxun_tiaojian::leixing_zileixing_chaxun(
            leixing.to_string(),
            zileixing.to_string(),
            fenye_canshu::new(1, 1), // 只需要总数，不需要实际数据
        );
        self.chaxun_zongshu(&chaxun_tiaojian).await
    }
}
