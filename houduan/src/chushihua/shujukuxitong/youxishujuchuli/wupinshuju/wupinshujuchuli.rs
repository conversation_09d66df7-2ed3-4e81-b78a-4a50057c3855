#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use super::wupinjiegouti::{
    wupin_jiben_xinxi, wupin_huizong_xinxi, wupin_wanzheng_xinxi,
    wupin_chaxun_jieguo, wupin_ziduan_yingshe
};
use super::wupin_redis_kongzhi::wupin_redis_kongzhi;
use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use sqlx::Row;
use std::collections::HashMap;

/// 物品数据管理器
pub struct wupin_shuju_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_kongzhi: Option<wupin_redis_kongzhi>,
}

impl wupin_shuju_guanli {
    /// 创建新的物品数据管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的物品数据管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: wupin_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 通用查询方法
    ///
    /// # 参数
    /// * `wupin_id` - 物品ID
    /// * `chaxun_moshi` - 查询模式：
    ///   - "quanbu_xinxi": 查询全部信息（同时查询item_name表和wupin_huizong表）
    ///   - 字段名列表（用逗号分隔）: 只查询wupin_huizong表的指定字段
    pub async fn tongyong_chaxun(&self, wupin_id: &str, chaxun_moshi: &str) -> anyhow::Result<wupin_chaxun_jieguo> {
        if chaxun_moshi == wupin_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi {
            self.chaxun_quanbu_xinxi(wupin_id).await
        } else {
            self.chaxun_zhiding_ziduan(wupin_id, chaxun_moshi).await
        }
    }

    /// 查询物品全部信息（带Redis缓存）
    async fn chaxun_quanbu_xinxi(&self, wupin_id: &str) -> anyhow::Result<wupin_chaxun_jieguo> {
        // 如果有Redis控制器，先尝试从Redis获取
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(wanzheng_xinxi)) = redis_kongzhi.huoqu_quanbu_xinxi(wupin_id).await {
                return Ok(wupin_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_redis.to_string(),
                    wanzheng_xinxi: Some(wanzheng_xinxi),
                    wupin_shuju: None,
                });
            }
        }

        // Redis中没有数据，从MySQL查询
        let jiben_xinxi = match self.chaxun_jiben_xinxi(wupin_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        let huizong_xinxi = match self.chaxun_huizong_xinxi(wupin_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        // 检查是否至少有一个表有数据
        if jiben_xinxi.is_none() && huizong_xinxi.is_none() {
            return Ok(wupin_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_wupin_bucunzai(wupin_id)),
                laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
                wanzheng_xinxi: None,
                wupin_shuju: None,
            });
        }

        let wanzheng_xinxi = wupin_wanzheng_xinxi {
            id: wupin_id.to_string(),
            jiben_xinxi,
            huizong_xinxi,
        };

        // 如果有Redis控制器，将查询结果存储到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let _ = redis_kongzhi.cunchu_quanbu_xinxi(wupin_id, &wanzheng_xinxi).await;
        }

        Ok(wupin_chaxun_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
            wanzheng_xinxi: Some(wanzheng_xinxi),
            wupin_shuju: None,
        })
    }

    /// 查询指定字段（只查询汇总表）
    async fn chaxun_zhiding_ziduan(&self, wupin_id: &str, ziduan_liebiao: &str) -> anyhow::Result<wupin_chaxun_jieguo> {
        let ziduan_mingcheng: Vec<&str> = ziduan_liebiao.split(',').map(|s| s.trim()).collect();

        // 验证字段是否都在汇总表中
        let huizong_biao_ziduan = wupin_ziduan_yingshe::huoqu_huizong_biao_ziduan();
        let mut youxiao_ziduan = Vec::new();

        for ziduan in &ziduan_mingcheng {
            if huizong_biao_ziduan.contains(&ziduan.to_string()) {
                youxiao_ziduan.push(*ziduan);
            }
        }

        if youxiao_ziduan.is_empty() {
            return Ok(wupin_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::cuowu_ziduan_bu_zai_huizong_biao.to_string()),
                laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
                wanzheng_xinxi: None,
                wupin_shuju: None,
            });
        }

        // 只查询汇总表字段
        match self.chaxun_huizong_biao_ziduan(wupin_id, &youxiao_ziduan).await {
            Ok(huizong_shuju) => {
                if huizong_shuju.is_empty() {
                    Ok(wupin_chaxun_jieguo {
                        chenggong: false,
                        cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_wupin_zai_huizong_biao_bucunzai(wupin_id)),
                        laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
                        wanzheng_xinxi: None,
                        wupin_shuju: None,
                    })
                } else {
                    Ok(wupin_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
                        wanzheng_xinxi: None,
                        wupin_shuju: Some(huizong_shuju),
                    })
                }
            }
            Err(_) => {
                Ok(wupin_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_wupin_zai_huizong_biao_bucunzai(wupin_id)),
                    laiyuan: wupin_zifuchuan_changliangguanli::shuju_laiyuan_mysql.to_string(),
                    wanzheng_xinxi: None,
                    wupin_shuju: None,
                })
            }
        }
    }

    /// 查询item_name表基础信息
    async fn chaxun_jiben_xinxi(&self, wupin_id: &str) -> anyhow::Result<wupin_jiben_xinxi> {
        let row = sqlx::query(wupin_zifuchuan_changliangguanli::sql_chaxun_jiben_xinxi)
            .bind(wupin_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                Ok(wupin_jiben_xinxi {
                    id: row.try_get::<String, _>("ID").unwrap_or_default(),
                    zhognwenming: row.try_get::<Option<String>, _>("zhognwenming").unwrap_or(None),
                    zhognwenjieshao: row.try_get::<Option<String>, _>("zhognwenjieshao").unwrap_or(None),
                })
            }
            None => Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::cuowu_wupin_jiben_xinxi_bucunzai))
        }
    }

    /// 查询wupin_huizong表汇总信息
    async fn chaxun_huizong_xinxi(&self, wupin_id: &str) -> anyhow::Result<wupin_huizong_xinxi> {
        let row = sqlx::query(wupin_zifuchuan_changliangguanli::sql_chaxun_huizong_xinxi)
            .bind(wupin_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                Ok(wupin_huizong_xinxi {
                    wupin_id: row.try_get::<Option<String>, _>("wupin_id").unwrap_or(None),
                    wupin_mingcheng: row.try_get::<Option<String>, _>("wupin_mingcheng").unwrap_or(None),
                    zhongwenming: row.try_get::<Option<String>, _>("zhongwenming").unwrap_or(None),
                    wanzhengxing_fenshu: row.try_get::<Option<String>, _>("wanzhengxing_fenshu").unwrap_or(None),
                    leixing: row.try_get::<Option<String>, _>("leixing").unwrap_or(None),
                    zileixing: row.try_get::<Option<String>, _>("zileixing").unwrap_or(None),
                    zhongliang: row.try_get::<Option<String>, _>("zhongliang").unwrap_or(None),
                    guaiwu_shuliang: row.try_get::<Option<String>, _>("guaiwu_shuliang").unwrap_or(None),
                    shangjia_shuliang: row.try_get::<Option<String>, _>("shangjia_shuliang").unwrap_or(None),
                    chongwu_shuliang: row.try_get::<Option<String>, _>("chongwu_shuliang").unwrap_or(None),
                    xiangzi_shuliang: row.try_get::<Option<String>, _>("xiangzi_shuliang").unwrap_or(None),
                    lishi_jilu_shuliang: row.try_get::<Option<String>, _>("lishi_jilu_shuliang").unwrap_or(None),
                    jichuxinxi_yaml: row.try_get::<Option<String>, _>("jichuxinxi_yaml").unwrap_or(None),
                    guaiwulaiyuan_yaml: row.try_get::<Option<String>, _>("guaiwulaiyuan_yaml").unwrap_or(None),
                    shoumaishangjia_yaml: row.try_get::<Option<String>, _>("shoumaishangjia_yaml").unwrap_or(None),
                    chongwu_yaml: row.try_get::<Option<String>, _>("chongwu_yaml").unwrap_or(None),
                    laiyuanxiangzi_yaml: row.try_get::<Option<String>, _>("laiyuanxiangzi_yaml").unwrap_or(None),
                    lishibiandong_yaml: row.try_get::<Option<String>, _>("lishibiandong_yaml").unwrap_or(None),
                    you_jichuxinxi: row.try_get::<Option<String>, _>("you_jichuxinxi").unwrap_or(None),
                    you_guaiwulaiyuan: row.try_get::<Option<String>, _>("you_guaiwulaiyuan").unwrap_or(None),
                    you_shoumaishangjia: row.try_get::<Option<String>, _>("you_shoumaishangjia").unwrap_or(None),
                    you_chongwu: row.try_get::<Option<String>, _>("you_chongwu").unwrap_or(None),
                    you_laiyuanxiangzi: row.try_get::<Option<String>, _>("you_laiyuanxiangzi").unwrap_or(None),
                    you_lishibiandong: row.try_get::<Option<String>, _>("you_lishibiandong").unwrap_or(None),
                })
            }
            None => Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::cuowu_wupin_huizong_xinxi_bucunzai))
        }
    }

    /// 查询item_name表指定字段
    async fn chaxun_jiben_biao_ziduan(&self, wupin_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = wupin_zifuchuan_changliangguanli::shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(wupin_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 查询wupin_huizong表指定字段
    async fn chaxun_huizong_biao_ziduan(&self, wupin_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = wupin_zifuchuan_changliangguanli::shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(wupin_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 批量查询物品
    pub async fn piliang_chaxun(&self, id_liebiao: Vec<String>, chaxun_moshi: &str) -> anyhow::Result<Vec<wupin_chaxun_jieguo>> {
        let mut jieguo_liebiao = Vec::new();

        for id in id_liebiao {
            let jieguo = self.tongyong_chaxun(&id, chaxun_moshi).await?;
            jieguo_liebiao.push(jieguo);
        }

        Ok(jieguo_liebiao)
    }

    /// 检查物品是否存在
    pub async fn jiancha_wupin_cunzai(&self, wupin_id: &str) -> anyhow::Result<bool> {
        // 检查item_name表
        let jiben_jieguo = sqlx::query(wupin_zifuchuan_changliangguanli::sql_jiancha_jiben_biao_cunzai)
            .bind(wupin_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let jiben_count: i64 = jiben_jieguo.get("count");
        if jiben_count > 0 {
            return Ok(true);
        }

        // 检查wupin_huizong表
        let huizong_jieguo = sqlx::query(wupin_zifuchuan_changliangguanli::sql_jiancha_huizong_biao_cunzai)
            .bind(wupin_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let huizong_count: i64 = huizong_jieguo.get("count");
        Ok(huizong_count > 0)
    }

    /// 获取支持的字段列表
    /// 注意：指定字段查询只支持汇总表字段，全部信息查询支持两个表的字段
    pub fn huoqu_zhichi_ziduan() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert(wupin_zifuchuan_changliangguanli::biao_ming_item_name.to_string(), wupin_ziduan_yingshe::huoqu_jiben_biao_ziduan());
        ziduan_map.insert(wupin_zifuchuan_changliangguanli::biao_ming_wupin_huizong.to_string(), wupin_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map.insert(wupin_zifuchuan_changliangguanli::biao_ming_zhiding_ziduan_zhichi.to_string(), wupin_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 清理物品全部数据获取的Redis缓存
    /// 只清除物品数据获取相关的缓存，不会清理其他缓存
    pub async fn qingchu_wupin_quanbu_xinxi_huancun(&self) -> anyhow::Result<u64> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.qingchu_wupin_quanbu_xinxi_huancun().await
            }
            None => Ok(0) // 没有Redis控制器，返回0
        }
    }

    /// 删除指定物品的全部信息缓存
    pub async fn shanchu_wupin_huancun(&self, wupin_id: &str) -> anyhow::Result<bool> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.shanchu_quanbu_xinxi(wupin_id).await
            }
            None => Ok(false) // 没有Redis控制器，返回false
        }
    }

    /// 获取物品缓存统计信息
    pub async fn huoqu_wupin_huancun_tongji(&self) -> anyhow::Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.huoqu_wupin_huancun_tongji().await
            }
            None => Ok(wupin_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }
}