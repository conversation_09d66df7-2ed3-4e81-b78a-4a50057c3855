use crate::chushihua::peizhi::peizhixitong_guanli::peizhixitong_guanli;
use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::{shujukuxitong_rizhi_xinxi, shujukuxitong_rizhi_cuowu};
use super::redis_lianjie::redis_lianjie_guanli;

pub struct redis_chushihua;

impl redis_chushihua {
    pub fn chushihua(peizhixitong: &peizhixitong_guanli) -> anyhow::Result<redis_lianjie_guanli> {
        shujukuxitong_rizhi_xinxi("redis", "开始初始化redis数据库连接...");

        let zongpeizhi = match peizhixitong.peizhixitong_get_peizhi() {
            Ok(peizhi) => peizhi,
            Err(e) => {
                let cuowu_xinxi = format!("获取配置信息失败: {}", e);
                shujukuxitong_rizhi_cuowu("redis", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        };

        let redis_peizhi = &zongpeizhi.redis;
        shujukuxitong_rizhi_xinxi(
            "redis",
            &format!(
                "redis配置信息 - 服务器: {}:{}",
                redis_peizhi.ip,
                redis_peizhi.duankou
            )
        );

        let mut redis_lianjie = redis_lianjie_guanli::new();

        match redis_lianjie.lianjie(redis_peizhi) {
            Ok(_) => {
                shujukuxitong_rizhi_xinxi("redis", "redis数据库连接池创建成功");
            }
            Err(e) => {
                let cuowu_xinxi = format!("redis数据库连接失败: {}", e);
                shujukuxitong_rizhi_cuowu("redis", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        }

        shujukuxitong_rizhi_xinxi("redis", "redis数据库初始化完成！");
        Ok(redis_lianjie)
    }
}