# 接口文档：技能数据 (`jiekou_jinengshuju.rs`)

## 1. 文件概述

该文件定义了与游戏技能数据相关的API接口。它提供了查询技能详细信息、分页获取技能列表、按条件搜索技能以及管理Redis缓存的功能。所有接口都支持从MySQL数据库查询，并利用Redis进行缓存以提高性能。

## 2. 接口列表

| HTTP 方法 | 路径                                                              | 描述                       | 功能                                                                         |
| :-------- | :---------------------------------------------------------------- | :------------------------- | :--------------------------------------------------------------------------- |
| `GET`     | `/youxishuju/jineng/xinxi/{id}`                                   | 查询技能全部信息           | 根据技能ID查询技能的全部信息。                                               |
| `GET`     | `/youxishuju/jineng/xinxi/{ziduan_ming}/{id}`                     | 查询技能指定字段信息       | 根据字段名和技能ID查询技能的指定字段信息。                                   |
| `GET`     | `/youxishuju/jineng/liebiao`                                      | 查询技能列表（分页）       | 分页查询技能列表。                                                           |
| `DELETE`  | `/youxishuju/jineng/liebiao/huancun`                              | 清除技能列表缓存           | 清除所有技能列表的Redis缓存。                                                |
| `GET`     | `/youxishuju/jineng/leiming/{leiming}`                            | 根据类名精确查询技能列表   | 根据技能类名精确查询技能列表，支持分页。                                     |
| `GET`     | `/youxishuju/jineng/mingzi/jingque/{mingzi}`                      | 根据名字精确查询技能列表   | 根据技能名字精确查询技能列表，支持分页。                                     |
| `GET`     | `/youxishuju/jineng/mingzi/mohu/{mingzi}`                         | 根据名字模糊查询技能列表   | 根据技能名字模糊查询技能列表，支持分页。                                     |
| `DELETE`  | `/youxishuju/jineng/mingzi/huancun`                               | 清除技能名字查询缓存       | 清除所有技能名字查询（包括类名、精确、模糊）的Redis缓存。                    |

---

## 3. 接口详解

### 3.1 查询技能全部信息

-   **功能描述**: 根据技能ID查询技能的全部信息，包括基础信息和汇总信息。
-   **请求路径**: `/youxishuju/jineng/xinxi/{id}`
-   **请求方法**: `GET`
-   **路径参数**:
    -   `id` (String, 必需): 要查询的技能ID。
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功查询到ID为 [some_id] 的技能全部信息",
        "shuju": {
            "chenggong": true,
            "cuowu_xinxi": null,
            "jineng_shuju": {
                // ... 技能的完整数据结构
            }
        }
    }
    ```
-   **失败响应示例 (404 Not Found)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "查询ID为 [some_id] 的技能失败",
        "shuju": null
    }
    ```

### 3.2 查询技能指定字段信息

-   **功能描述**: 根据字段名和技能ID查询技能的指定字段信息。
-   **请求路径**: `/youxishuju/jineng/xinxi/{ziduan_ming}/{id}`
-   **请求方法**: `GET`
-   **路径参数**:
    -   `ziduan_ming` (String, 必需): 要查询的字段名称。有效字段名由 `jineng_ziduan_yingshe` 定义。
    -   `id` (String, 必需): 要查询的技能ID。
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功查询到ID为 [some_id] 的技能字段 [ziduan_ming] 的信息",
        "shuju": {
            "chenggong": true,
            "cuowu_xinxi": null,
            "jineng_shuju": {
                // ... 包含指定字段的技能数据
            }
        }
    }
    ```
-   **失败响应示例 (400 Bad Request)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "无效的字段名: [ziduan_ming]",
        "shuju": null
    }
    ```

### 3.3 查询技能列表（分页）

-   **功能描述**: 分页查询技能列表，支持Redis缓存。
-   **请求路径**: `/youxishuju/jineng/liebiao`
-   **请求方法**: `GET`
-   **查询参数**:
    -   `yema` (u32, 可选, 默认: 1): 页码，从1开始。
    -   `meiye_shuliang` (u32, 可选, 默认: 10): 每页数量，范围 1-100。
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功查询到第 1 页的 10 个技能",
        "shuju": {
            "chenggong": true,
            "cuowu_xinxi": null,
            "jineng_liebiao": [
                // ... 技能列表
            ],
            "zong_ye": 10,
            "zong_shu": 100
        }
    }
    ```
-   **失败响应示例 (400 Bad Request)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "每页数量必须在 1 到 100 之间",
        "shuju": null
    }
    ```

### 3.4 清除技能列表缓存

-   **功能描述**: 清除所有技能列表的Redis缓存，不影响技能详情缓存。
-   **请求路径**: `/youxishuju/jineng/liebiao/huancun`
-   **请求方法**: `DELETE`
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功清除技能列表缓存",
        "shuju": null
    }
    ```
-   **失败响应示例 (500 Internal Server Error)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "清除技能列表缓存时发生错误: [error_details]",
        "shuju": null
    }
    ```

### 3.5 根据类名/名字查询技能列表

-   **功能描述**: 根据类名或名字（精确/模糊）查询技能列表，支持分页和Redis缓存。
-   **请求路径**:
    -   `/youxishuju/jineng/leiming/{leiming}` (类名精确)
    -   `/youxishuju/jineng/mingzi/jingque/{mingzi}` (名字精确)
    -   `/youxishuju/jineng/mingzi/mohu/{mingzi}` (名字模糊)
-   **请求方法**: `GET`
-   **路径参数**:
    -   `leiming` / `mingzi` (String, 必需): 查询的关键词。
-   **查询参数**:
    -   `yema` (u32, 可选, 默认: 1): 页码。
    -   `meiye_shuliang` (u32, 可选, 默认: 10): 每页数量。
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功通过 [查询类型] 查询到 [N] 个名为 [keyword] 的技能",
        "shuju": {
            "chenggong": true,
            "cuowu_xinxi": null,
            "jineng_liebiao": [
                // ... 技能列表
            ],
            "zong_ye": 5,
            "zong_shu": 45
        }
    }
    ```
-   **失败响应示例 (404 Not Found)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "通过 [查询类型] 查询名为 [keyword] 的技能失败",
        "shuju": null
    }
    ```

### 3.6 清除技能名字查询缓存

-   **功能描述**: 清除所有技能名字查询（包括类名、名字精确、名字模糊）的Redis缓存。
-   **请求路径**: `/youxishuju/jineng/mingzi/huancun`
-   **请求方法**: `DELETE`
-   **成功响应示例 (200 OK)**:
    ```json
    {
        "chenggong": true,
        "xinxi": "成功清除技能名字查询缓存",
        "shuju": null
    }
    ```
-   **失败响应示例 (500 Internal Server Error)**:
    ```json
    {
        "chenggong": false,
        "xinxi": "清除技能名字查询缓存时发生错误: [error_details]",
        "shuju": null
    }
    ```