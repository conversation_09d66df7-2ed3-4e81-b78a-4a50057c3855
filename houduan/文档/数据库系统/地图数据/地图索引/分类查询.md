# 地图分类查询 (`ditu_fenlei_chaxun`)

## 概述

`ditu_fenlei_chaxun` 结构体是地图数据查询系统的核心组件之一，专门用于处理基于多种分类条件的动态查询请求。它能够根据客户端传递的参数，动态构建 SQL 查询语句，从 `ditu_huizong` 表中筛选出符合条件的地图数据，并提供分页和缓存功能。

该模块的设计目标是提供一个高效、灵活且可扩展的地图分类查询解决方案，支持复杂的逻辑组合，并通过 Redis 缓存机制显著提升高频查询的响应速度。

## 核心功能

- **动态条件查询**: 支持基于一个或多个分类字段进行查询。
- **逻辑关系支持**: 允许查询条件之间使用 `AND` 或 `OR` 逻辑关系进行组合。
- **分页功能**: 支持对查询结果进行分页，控制每页显示的数量。
- **结果缓存**: 集成 Redis 缓存，将查询结果缓存一段时间，减少数据库压力。
- **缓存管理**: 提供清除全部分类查询缓存的功能。

## SQL 构建逻辑

SQL 查询的构建是该模块最核心的部分，主要由 `gouzao_where_ziju` 方法驱动。

### 1. 条件验证

在构建 SQL 之前，系统会首先验证查询参数中的每个字段名是否合法。通过调用 `yanzheng_fenlei_ziduan` 方法，将传入的字段名与一个预定义的有效字段列表进行比对，确保查询的安全性，防止 SQL 注入。

合法的分类字段包括：
```
"leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
"leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
"fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
"fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi"
```

### 2. WHERE 子句构造

`gouzao_where_ziju` 方法根据验证通过的条件动态生成 SQL 的 `WHERE` 子句。

- **遍历条件**: 遍历 `ditu_fenlei_chaxun_canshu` 中的 `fenlei_tiaojian` 列表。
- **生成子句**: 为每个条件生成一个 `字段名 = ?` 格式的子句，并将对应的值（`true` 或 `false`）存入绑定参数列表。
- **逻辑拼接**: 根据 `luoji_guanxi` 参数（默认为 `AND`，可指定为 `OR`），将所有子句拼接起来，形成最终的 `WHERE` 语句。

**示例**:
如果查询参数为：
- `fenlei_tiaojian`: `[{"ziduan_ming": "leixing_town", "zhi": true}, {"ziduan_ming": "fenlei_pvp", "zhi": true}]`
- `luoji_guanxi`: `"OR"`

生成的 `WHERE` 子句将是：
```sql
WHERE leixing_town = ? OR fenlei_pvp = ?
```
对应的绑定值为 `["true", "true"]`。

### 3. 完整 SQL 生成

`gouzao_fenlei_chaxun_sql` 和 `gouzao_fenlei_zongshu_sql` 方法利用上述 `WHERE` 子句，分别构建用于查询地图列表和总数量的完整 SQL 语句，并附加 `LIMIT` 和 `OFFSET` 实现分页。

## 缓存键生成策略

为了确保每个独特的查询请求都能对应一个独立的缓存条目，`ditu_fenlei_chaxun` 采用了一套精密的缓存键生成策略。

缓存键由 `shengcheng_huancun_jian` 方法生成，其格式如下：

`ditu_fenlei:{tiaojian_hash}:{dangqian_ye}:{mei_ye_shuliang}`

- **`ditu_fenlei:`**: 静态前缀，用于标识这是地图分类查询的缓存。
- **`{tiaojian_hash}`**: 查询条件的哈希值。该哈希值由 `ditu_fenlei_chaxun_canshu` 的 `shengcheng_tiaojian_hash` 方法生成，它会将所有分类条件（字段名和值）以及逻辑关系（`AND`/`OR`）序列化后计算哈希。**这确保了任何查询条件的变动（包括条件的增删、值的改变、顺序的改变以及逻辑关系的改变）都会产生一个全新的哈希值**，从而避免缓存污染。
- **`{dangqian_ye}`**: 当前查询的页码。
- **`{mei_ye_shuliang}`**: 每页显示的数量。

这种策略保证了只有当 **查询条件、页码、每页数量** 完全相同时，才会命中同一个缓存，确保了数据的准确性。

## 使用流程

1.  **实例化**: 创建 `ditu_fenlei_chaxun` 实例，可以选择是否传入 Redis 连接实例以启用缓存。
2.  **构造参数**: 创建并填充 `ditu_fenlei_chaxun_canshu` 结构体，设置查询条件、逻辑关系和分页参数。
3.  **执行查询**: 调用 `chaxun_fenlei_ditu_liebiao` 方法并传入参数。
4.  **处理结果**:
    -   如果启用了 Redis 且缓存命中，则直接从 Redis 返回结果。
    -   如果缓存未命中，则执行数据库查询，获取数据。
    -   将从数据库获取的结果序列化为 JSON，并存入 Redis（默认缓存1小时）。
    -   向调用方返回最终的 `ditu_liebiao_jieguo`。