# `ditu_sql_kongzhi` 结构体文档

本文档详细说明了 `ditu_sql_kongzhi` 结构体的功能，以及其中定义的每个方法、常量和SQL查询的用途。

## 概述

`ditu_sql_kongzhi` 是一个专门用于处理地图数据相关SQL查询的工具类。它不包含任何状态，所有方法都是静态的。其主要目的是封装和管理与 `ditu_huizong` 和 `ditu_name` 表相关的SQL查询逻辑，提供统一、安全、易于维护的SQL生成和验证功能。

## 常量

### `quanbu_xinxi_ziduan`

- **类型**: `&'static str`
- **值**: `"quanbu_xinxi"`
- **用途**: 这是一个特殊的字段名常量。当外部请求使用此字段名时，系统应理解为需要获取指定地图ID的所有相关信息，而不是查询单个字段。

## 方法

### SQL查询模板

#### `huoqu_huizong_ziduan_sql()`

- **返回**: `&'static str`
- **SQL**: `SELECT {} FROM ditu_huizong WHERE ditu_id = ?`
- **用途**: 提供一个用于查询 `ditu_huizong` 表中**单个**字段的SQL模板。需要使用 `format!` 宏将 `{}` 替换为实际的字段名。

#### `huoqu_huizong_quanbu_sql()`

- **返回**: `&'static str`
- **SQL**: `SELECT * FROM ditu_huizong WHERE ditu_id = ?`
- **用途**: 提供一个用于查询 `ditu_huizong` 表中**所有**字段的SQL语句。

#### `huoqu_name_quanbu_sql()`

- **返回**: `&'static str`
- **SQL**: `SELECT * FROM ditu_name WHERE id = ?`
- **用途**: 提供一个用于查询 `ditu_name` 表中**所有**字段的SQL语句。

#### `jiancha_ziduan_cunzai_sql()`

- **返回**: `&'static str`
- **SQL**: `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'xianjingziliaozhan' AND TABLE_NAME = 'ditu_huizong' AND COLUMN_NAME = ?`
- **用途**: 提供一个查询数据库元信息（`INFORMATION_SCHEMA.COLUMNS`）的SQL语句，用于检查指定的字段名是否存在于 `ditu_huizong` 表中。这是一种动态验证字段有效性的方法。

### 动态SQL构建

#### `gouzao_ziduan_chaxun_sql(ziduan_ming: &str) -> String`

- **参数**:
    - `ziduan_ming`: 要查询的字段名。
- **返回**: `String`
- **用途**: 根据传入的字段名动态构建一个完整的SQL查询语句。它使用 `huoqu_huizong_ziduan_sql` 的模板，并将字段名填充进去。
- **注意**: 此方法自身**不进行**任何安全验证，调用者必须在使用前确保 `ziduan_ming` 的安全性。

### 安全性与验证

#### `yanzheng_ziduan_ming_anquan(ziduan_ming: &str) -> bool`

- **参数**:
    - `ziduan_ming`: 需要验证的字段名。
- **返回**: `bool`
- **用途**: 这是一个核心的安全函数，用于防止SQL注入攻击。它通过检查字段名的每个字符来确保其只包含字母、数字或下划线。
- **安全策略**: 只允许最基本的字符集，有效阻止了如 `;`, `--`, ` ` (空格) 等恶意字符的注入。

#### `jiancha_ziduan_hefa(ziduan_ming: &str) -> bool`

- **参数**:
    - `ziduan_ming`: 需要验证的字段名。
- **返回**: `bool`
- **用途**: 检查传入的字段名是否是 `ditu_huizong` 表中一个已知的、合法的字段。
- **验证逻辑**:
    1. 首先检查字段名是否是特殊的 `quanbu_xinxi_ziduan` 常量，如果是，则认为是合法的。
    2. 如果不是，则会查询内部预定义的 `ditu_huizong` 表所有字段的列表（通过 `huoqu_huizong_suoyou_ziduan` 方法获取），判断字段名是否存在于该列表中。
- **安全层级**: 这是在 `yanzheng_ziduan_ming_anquan` 基础之上的第二层保护，确保了即使字段名字符安全，它也必须是业务逻辑上允许查询的字段。

### 辅助方法

#### `huoqu_huizong_suoyou_ziduan() -> Vec<&'static str>`

- **返回**: `Vec<&'static str>`
- **用途**: 返回一个静态的字符串向量，其中包含了 `ditu_huizong` 表所有已知的字段名。此列表用于 `jiancha_ziduan_hefa` 方法进行字段合法性校验。