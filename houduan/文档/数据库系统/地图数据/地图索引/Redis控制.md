# `ditu_redis_kongzhi` - 地图数据Redis缓存控制

本文档详细说明了 `ditu_redis_kongzhi` 结构体的设计和功能，该结构体负责管理游戏中地图数据的Redis缓存。

## 目录
- [结构体概述](#结构体概述)
- [常量定义](#常量定义)
- [缓存键生成规则](#缓存键生成规则)
- [公共方法](#公共方法)
  - [new](#new)
  - [shengcheng_quanbu_xinxi_jian](#shengcheng_quanbu_xinxi_jian)
  - [shengcheng_liebiao_jian](#shengcheng_liebiao_jian)
  - [shezhi_ditu_quanbu_xinxi](#shezhi_ditu_quanbu_xinxi)
  - [huoqu_ditu_quanbu_xinxi](#huoqu_ditu_quanbu_xinxi)
  - [shanchu_ditu_quanbu_xinxi](#shanchu_ditu_quanbu_xinxi)
  - [jiancha_ditu_huancun_cunzai](#jiancha_ditu_huancun_cunzai)
  - [qingchu_suoyou_ditu_huancun](#qingchu_suoyou_ditu_huancun)
  - [huoqu_ditu_huancun_tongji](#huoqu_ditu_huancun_tongji)
  - [shezhi_ditu_liebiao](#shezhi_ditu_liebiao)
  - [huoqu_ditu_liebiao](#huoqu_ditu_liebiao)
  - [shanchu_suoyou_ditu_liebiao_huancun](#shanchu_suoyou_ditu_liebiao_huancun)
  - [shezhi_zidingyijian](#shezhi_zidingyijian)
  - [huoqu_zidingyijian](#huoqu_zidingyijian)
  - [shanchu_pipei_jian](#shanchu_pipei_jian)
- [缓存策略](#缓存策略)

---

## 结构体概述

`ditu_redis_kongzhi` 是一个专门用于处理地图数据在Redis中缓存的控制器。它封装了所有与Redis交互的逻辑，包括数据的增、删、改、查，以及缓存键的管理。通过这个结构体，可以有效地将地图数据缓存到Redis，减少对主数据库的访问压力，提高数据检索速度。

该结构体依赖于 `redis_lianjie_guanli` 来获取和管理Redis连接。

```rust
pub struct ditu_redis_kongzhi {
    redis_lianjie: redis_lianjie_guanli,
}
```

## 常量定义

为了统一管理缓存的有效期，定义了以下常量：

- **`quanbu_shuju_huancun_shijian`**: `259200` 秒 (3天)
  - 用于单个地图的完整信息的缓存时间。
- **`liebiao_huancun_shijian`**: `3600` 秒 (1小时)
  - 用于地图列表（分页数据）的缓存时间。

## 缓存键生成规则

缓存键的设计遵循统一的命名规范，以方便管理和识别。

- **单个地图完整信息**:
  - **规则**: `ditu_quanbu_xinxi:{ditu_id}`
  - **示例**: `ditu_quanbu_xinxi:1001`
  - **生成方法**: `shengcheng_quanbu_xinxi_jian(ditu_id: &str)`

- **地图列表**:
  - **规则**: `ditu_liebiao:{mei_ye_shuliang}:{dangqian_ye}`
  - **示例**: `ditu_liebiao:10:1`
  - **生成方法**: `shengcheng_liebiao_jian(mei_ye_shuliang: u32, dangqian_ye: u32)`

- **批量操作模式匹配**:
  - **地图数据**: `ditu_quanbu_xinxi:*`
  - **列表数据**: `ditu_liebiao:*`

## 公共方法

### `new`
创建一个 `ditu_redis_kongzhi` 的新实例。

- **参数**:
  - `redis_lianjie: redis_lianjie_guanli`: Redis连接管理器实例。
- **返回**: `Self`

### `shengcheng_quanbu_xinxi_jian`
生成用于存储单个地图完整信息的Redis键。

- **参数**:
  - `ditu_id: &str`: 地图的唯一ID。
- **返回**: `String` - 生成的Redis键。

### `shengcheng_liebiao_jian`
生成用于存储地图列表（分页）的Redis键。

- **参数**:
  - `mei_ye_shuliang: u32`: 每页显示的数量。
  - `dangqian_ye: u32`: 当前页码。
- **返回**: `String` - 生成的Redis键。

### `shezhi_ditu_quanbu_xinxi`
将单个地图的完整信息（通常是JSON字符串）存入Redis，并设置过期时间。

- **参数**:
  - `ditu_id: &str`: 地图ID。
  - `shuju: &str`: 要缓存的地图数据。
- **返回**: `Result<()>`

### `huoqu_ditu_quanbu_xinxi`
从Redis中获取单个地图的完整信息。

- **参数**:
  - `ditu_id: &str`: 地图ID。
- **返回**: `Result<Option<String>>` - 如果缓存命中，返回数据；否则返回 `None`。

### `shanchu_ditu_quanbu_xinxi`
从Redis中删除指定地图的缓存。

- **参数**:
  - `ditu_id: &str`: 地图ID。
- **返回**: `Result<bool>` - 如果成功删除，返回 `true`。

### `jiancha_ditu_huancun_cunzai`
检查指定地图的缓存是否存在于Redis中。

- **参数**:
  - `ditu_id: &str`: 地图ID。
- **返回**: `Result<bool>` - 如果存在，返回 `true`。

### `qingchu_suoyou_ditu_huancun`
清除所有与地图相关的缓存（使用 `ditu_quanbu_xinxi:*` 模式匹配）。

- **返回**: `Result<u64>` - 返回被删除的键的数量。

### `huoqu_ditu_huancun_tongji`
获取当前地图数据缓存的统计信息（缓存键的数量）。

- **返回**: `Result<String>` - 包含统计信息的描述字符串。

### `shezhi_ditu_liebiao`
将地图列表（分页数据）存入Redis，并设置过期时间。

- **参数**:
  - `mei_ye_shuliang: u32`: 每页数量。
  - `dangqian_ye: u32`: 当前页码。
  - `shuju: &str`: 要缓存的列表数据。
- **返回**: `Result<()>`

### `huoqu_ditu_liebiao`
从Redis中获取地图列表。

- **参数**:
  - `mei_ye_shuliang: u32`: 每页数量。
  - `dangqian_ye: u32`: 当前页码。
- **返回**: `Result<Option<String>>` - 如果缓存命中，返回数据；否则返回 `None`。

### `shanchu_suoyou_ditu_liebiao_huancun`
清除所有地图列表的缓存（使用 `ditu_liebiao:*` 模式匹配）。

- **返回**: `Result<u64>` - 返回被删除的键的数量。

### `shezhi_zidingyijian`
提供一个通用方法，用于设置自定义的键值对到Redis，并指定过期时间。

- **参数**:
  - `jian: &str`: 自定义键。
  - `zhi: &str`: 要存储的值。
  - `guoqi_shijian: i64`: 过期时间（秒）。
- **返回**: `Result<()>`

### `huoqu_zidingyijian`
提供一个通用方法，用于从Redis获取自定义键的值。

- **参数**:
  - `jian: &str`: 自定义键。
- **返回**: `Result<Option<String>>`

### `shanchu_pipei_jian`
根据指定的模式删除匹配的Redis键。

- **参数**:
  - `moshi: &str`: 要匹配的模式，例如 `ditu_*`。
- **返回**: `Result<u64>` - 返回被删除的键的数量。

## 缓存策略

- **数据一致性**: 缓存数据通过设置过期时间来自动失效。当数据源（如MySQL数据库）中的地图数据发生变更时，应主动调用相应的删除方法（如 `shanchu_ditu_quanbu_xinxi` 或 `shanchu_suoyou_ditu_liebiao_huancun`）来清除旧缓存，以确保数据一致性。
- **缓存穿透**: `huoqu_*` 方法在未命中缓存时会返回 `None`，调用方需要处理这种情况，从主数据源获取数据并回填到缓存中。
- **日志记录**: 所有关键的Redis操作（如设置、获取、删除、命中、未命中）都会通过 `ditu_rizhi_kongzhi` 进行日志记录，便于追踪和调试。
- **性能优化**:
  - 对频繁访问但不经常变更的数据（如地图详情）设置较长的缓存时间（3天）。
  - 对可能经常变化的列表数据设置较短的缓存时间（1小时）。