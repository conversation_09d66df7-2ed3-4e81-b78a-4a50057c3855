# 地图分类查询测试 (`ditu_fenlei_ceshi`)

本文档详细说明了 `ditu_fenlei_ceshi.rs` 文件中定义的测试套件，该套件用于验证地图分类查询功能的正确性、健壮性和性能。

## 概述

`ditu_fenlei_ceshi` 结构体是一个测试工具，旨在全面覆盖 `ditu_fenlei_chaxun` 模块的各种功能。它通过一系列精心设计的测试用例，确保地图分类查询在不同场景下都能返回预期的结果，并验证了缓存机制的有效性。

所有测试日志都会输出到控制台，并保存到 `rizhi/ditu_fenlei_ceshi_YYYY-MM-DD_HH-MM-SS.log` 文件中。

## 测试入口点

### `wanzheng_fenlei_ceshi`

这是整个测试流程的入口函数。它按顺序调用所有独立的测试函数，模拟一个完整的功能验证周期。

- **目的**: 提供一个一键执行所有分类查询相关测试的便捷方法。
- **执行流程**:
    1. 初始化日志记录器。
    2. 根据是否提供了 Redis 连接管理器，创建带缓存或不带缓存的 `ditu_fenlei_chaxun` 实例。
    3. 依次调用以下测试函数：
        - `ceshi_suoyou_leixing_ziduan`
        - `ceshi_suoyou_fenlei_ziduan`
        - `ceshi_duoge_fenlei_and_chaxun`
        - `ceshi_duoge_fenlei_or_chaxun`
        - `ceshi_redis_huancun_xingneng`
        - `ceshi_huancun_qingchu`
        - `ceshi_bianjie_tiaojian`
    4. 在所有测试结束后，执行最终的缓存清理操作。
    5. 保存日志文件并打印到控制台。

---

## 详细测试函数说明

### 1. `ceshi_suoyou_leixing_ziduan`

- **目的**: 验证对所有 `leixing_*` (类型) 字段的单独查询功能是否正常。
- **测试场景**:
    - 遍历一个预定义的地图类型字段列表（如 `leixing_town`, `leixing_field`, `leixing_dungeon` 等）。
    - 对每个字段，构造一个查询条件，例如 `leixing_town = true`。
- **验证逻辑**:
    - 检查查询是否成功执行，没有返回错误。
    - 记录查询耗时。
    - 记录并验证返回的地图总数和当前页的地图数量。
    - 如果有结果，打印前3个地图的 ID 和名称作为示例。

### 2. `ceshi_suoyou_fenlei_ziduan`

- **目的**: 验证对所有 `fenlei_*` (分类) 字段的单独查询功能是否正常。
- **测试场景**:
    - 遍历一个预定义的地图分类字段列表（如 `fenlei_town`, `fenlei_field`, `fenlei_dungeon` 等）。
    - 对每个字段，构造一个查询条件，例如 `fenlei_town = true`。
- **验证逻辑**:
    - 与 `ceshi_suoyou_leixing_ziduan` 类似，检查查询是否成功，并记录耗时、结果数量和示例数据。

### 3. `ceshi_duoge_fenlei_and_chaxun`

- **目的**: 测试使用 `AND` 逻辑组合多个查询条件的正确性。
- **测试场景**:
    - **场景一**: 查询同时满足 `leixing_town = true` 和 `fenlei_town = true` 的地图。
    - **场景二**: 查询同时满足 `leixing_field = true` 和 `fenlei_field = true` 的地图。
    - **场景三**: 查询同时满足 `leixing_dungeon = true` 和 `fenlei_dungeon = true` 的地图。
- **验证逻辑**:
    - 确认查询能够正确处理多个 `AND` 条件。
    - 验证返回的结果是否符合所有指定的条件。
    - 记录耗时、结果数量，并打印前5个地图作为示例。

### 4. `ceshi_duoge_fenlei_or_chaxun`

- **目的**: 测试使用 `OR` logic 组合多个查询条件的正确性。
- **测试场景**:
    - **场景一**: 查询满足 `leixing_town = true` 或 `leixing_dungeon = true` 的地图。
    - **场景二**: 查询满足 `leixing_field = true` 或 `leixing_instance = true` 或 `leixing_pvp = true` 的地图。
    - **场景三**: 查询满足 `leixing_quest = true` 或 `leixing_siege = true` 或 `leixing_other = true` 的地图。
- **验证逻辑**:
    - 确认查询能够正确处理多个 `OR` 条件。
    - 验证返回的结果是否至少满足其中一个指定条件。
    - 记录耗时、结果数量，并打印示例数据。

### 5. `ceshi_redis_huancun_xingneng`

- **目的**: 验证 Redis 缓存机制是否正常工作，并对比有无缓存时的查询性能。
- **测试场景**:
    - **第一次查询**: 使用特定条件（`leixing_field = true`）进行查询。此时数据应从数据库中获取，并写入 Redis 缓存。
    - **第二次查询**: 使用完全相同的条件再次进行查询。此时数据应直接从 Redis 缓存中获取。
- **验证逻辑**:
    - 记录两次查询的耗时。
    - **预期结果**: 第二次查询的耗时应远小于第一次查询的耗时。
    - 验证两次查询返回的结果是否完全一致。
    - 打印完整的返回结果以供详细比对。

### 6. `ceshi_huancun_qingchu`

- **目的**: 测试清除所有地图分类查询缓存的功能。
- **测试场景**:
    - 调用 `qingchu_fenlei_huancun` 方法。
- **验证逻辑**:
    - 检查该方法是否成功执行。
    - 记录成功清除的缓存条目数量。
    - 如果此测试后立即执行 `ceshi_redis_huancun_xingneng`，第一次查询应从数据库获取数据，证明缓存已被清除。

### 7. `ceshi_bianjie_tiaojian`

- **目的**: 测试系统在处理一些边界或异常查询条件时的鲁棒性。
- **测试场景**:
    - **空条件查询**: 构造一个不包含任何条件的查询请求。
    - **无效字段名查询**: 使用一个数据库表中不存在的字段名（如 `invalid_field`）进行查询。
- **验证逻辑**:
    - **空条件**: 预期应返回所有地图的分页列表，且不应报错。
    - **无效字段**: 预期查询会失败，并返回一个明确的错误信息，指出字段无效。系统不应崩溃。