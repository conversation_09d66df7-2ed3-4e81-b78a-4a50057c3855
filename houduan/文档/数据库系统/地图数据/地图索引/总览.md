# 地图数据模块总览

`ditushuju` 模块负责处理游戏中的地图相关数据，包括从数据库和缓存中查询地图信息、处理数据以及提供测试功能。

## 模块文件列表

以下是 `ditushuju` 模块内的主要源文件及其功能简介：

- **`ditushujujiegouti.rs`**
  - **功能**: 定义了地图数据的核心数据结构。
  - **详细文档**: [数据结构.md](./数据结构.md)

- **`ditushujuchuli.rs`**
  - **功能**: 模块的主处理器，协调其他子模块完成地图数据的获取和处理。
  - **详细文档**: [数据处理器.md](./数据处理器.md)

- **`ditu_sql_kongzhi.rs`**
  - **功能**: 封装了与地图数据相关的SQL查询逻辑。
  - **详细文档**: [SQL控制.md](./SQL控制.md)

- **`ditu_redis_kongzhi.rs`**
  - **功能**: 管理地图数据的Redis缓存，包括读取和写入操作。
  - **详细文档**: [Redis控制.md](./Redis控制.md)

- **`ditu_liebiao_chaxun.rs`**
  - **功能**: 提供查询地图列表的功能。
  - **详细文档**: [列表查询.md](./列表查询.md)

- **`ditu_fenlei_chaxun.rs`**
  - **功能**: 提供按分类查询地图数据的功能。
  - **详细文档**: [分类查询.md](./分类查询.md)

- **`ditu_fenlei_ceshi.rs`**
  - **功能**: 包含用于测试地图分类查询功能的代码。
  - **详细文档**: [分类测试.md](./分类测试.md)

- **`ditu_rizhi_kongzhi.rs`**
  - **功能**: 负责本地图模块的日志记录。
  - **详细文档**: [日志控制.md](./日志控制.md)

- **`mod.rs`**
  - **功能**: Rust模块声明文件，用于组织和暴露模块的公共接口。