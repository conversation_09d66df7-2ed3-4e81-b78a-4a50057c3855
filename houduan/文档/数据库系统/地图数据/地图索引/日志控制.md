# `ditu_rizhi_kongzhi` 日志控制模块详解

本文档详细介绍了 `ditu_rizhi_kongzhi` 结构体的作用，以及其中定义的每个日志记录方法和常量的目的。

## 结构体: `ditu_rizhi_kongzhi`

这是一个用于地图数据处理日志控制的结构体。它本身不包含任何字段，主要通过实现的方法来提供日志记录功能。

### 模块名称常量

- `mokuai_ming`: "地图数据库操作"
  - 定义了日志记录中使用的模块名称。

---

## 常量定义

### 数据来源

- `shuju_laiyuan_huancun`: "缓存"
  - 表示数据来源于缓存。
- `shuju_laiyuan_shujuku`: "数据库"
  - 表示数据来源于数据库。

### 操作消息

- `chenggong_qingchu_ditu_huancun_xiaoxi`: "成功清除地图缓存"
  - 用于表示成功清除地图缓存的消息。
- `ditu_huancun_bu_cunzai_xiaoxi`: "地图缓存不存在或已被清除"
  - 用于表示地图缓存不存在或已被清除的消息。
- `redis_lianjie_wei_peizhi_xiaoxi`: "Redis连接未配置"
  - 用于表示Redis连接未配置的消息。
- `redis_wei_peizhi_wufa_qingchu_xiaoxi`: "Redis连接未配置，无法清除缓存"
  - 用于表示因Redis未配置而无法清除缓存的消息。

### 错误消息前缀

- `ziduan_ming_bu_hefa_cuowu_qianzhui`: "字段名不合法或不存在"
  - 用于构建字段名不合法或不存在的错误消息。
- `ziduan_ming_bu_anquan_cuowu_qianzhui`: "字段名包含不安全字符"
  - 用于构建字段名包含不安全字符的错误消息。
- `qingchu_ditu_huancun_shibai_qianzhui`: "清除地图缓存失败"
  - 用于构建清除地图缓存失败的错误消息。

### 字段与键名

- `name_biao_id_ziduan`: "name_table_id"
  - `name` 表的ID字段名。
- `name_biao_ziduan_qianzhui`: "name_table_"
  - `name` 表的字段前缀。
- `id_ziduan_ming`: "id"
  - 通用的ID字段名。
- `redis_ditu_jian_qianzhui`: "ditu_quanbu:"
  - Redis中地图缓存键的前缀。
- `redis_ditu_moshi_pipei`: "ditu_quanbu:*"
  - 用于匹配所有地图缓存的Redis模式。

### 格式化字符串

- `ditu_quanbu_xinxi_huancun_tongji_geshi`: "地图全部信息缓存: {} 个"
  - 用于格式化地图全部信息缓存的统计信息。
- `huoqu_ditu_huancun_tongji_shibai_geshi`: "获取地图缓存统计失败: {}"
  - 用于格式化获取地图缓存统计失败的消息。

---

## 成功日志方法

这些方法用于记录操作成功的日志信息。

- `huoqu_ditu_ziduan_chenggong(ditu_id: &str, ziduan_ming: &str)`
  - 记录成功获取地图单个字段的日志。
- `huoqu_ditu_quanbu_xinxi_chenggong(ditu_id: &str, laiyuan: &str)`
  - 记录成功获取地图全部信息的日志，并指明数据来源。
- `huancun_ditu_shuju_chenggong(ditu_id: &str)`
  - 记录成功缓存地图数据的日志。
- `qingchu_ditu_huancun_chenggong(ditu_id: &str)`
  - 记录成功清除地图缓存的日志。
- `redis_huancun_minzhong(ditu_id: &str)`
  - 记录Redis缓存命中的日志。
- `redis_huancun_wei_minzhong(ditu_id: &str)`
  - 记录Redis缓存未命中的日志。
- `redis_shezhi_huancun_chenggong(ditu_id: &str, guoqi_shijian: i64)`
  - 记录成功设置Redis缓存及其过期时间的日志。
- `redis_shanchu_huancun_chenggong(ditu_id: &str)`
  - 记录成功删除Redis缓存的日志。
- `redis_jiancha_huancun_cunzai(ditu_id: &str, cunzai: bool)`
  - 记录检查Redis缓存是否存在及其结果的日志。
- `redis_piliang_qingchu_chenggong(shuliang: u64)`
  - 记录成功批量清除Redis缓存的日志。
- `redis_huoqu_tongji_chenggong(tongji_xinxi: &str)`
  - 记录成功获取Redis统计信息的日志。

---

## 错误日志方法

这些方法用于记录操作失败或错误情况的日志。

- `huoqu_ditu_ziduan_shibai(ditu_id: &str, ziduan_ming: &str, cuowu: &str)`
  - 记录获取地图单个字段失败的日志。
- `huoqu_ditu_quanbu_xinxi_shibai(ditu_id: &str, cuowu: &str)`
  - 记录获取地图全部信息失败的日志。
- `ziduan_ming_yanzheng_shibai(ziduan_ming: &str)`
  - 记录字段名验证失败的日志。
- `ziduan_ming_bu_hefa_cuowu(ziduan_ming: &str) -> String`
  - 返回一个表示字段名不合法的错误消息字符串。
- `ziduan_ming_bu_anquan_cuowu(ziduan_ming: &str) -> String`
  - 返回一个表示字段名包含不安全字符的错误消息字符串。
- `qingchu_ditu_huancun_shibai_xiaoxi(cuowu: &str) -> String`
  - 返回一个表示清除地图缓存失败的错误消息字符串。
- `huancun_ditu_shuju_shibai(ditu_id: &str, cuowu: &str)`
  - 记录缓存地图数据失败的日志。
- `qingchu_ditu_huancun_shibai(ditu_id: &str, cuowu: &str)`
  - 记录清除地图缓存失败的日志。
- `ditu_shuju_bu_cunzai(ditu_id: &str)`
  - 记录地图数据不存在的日志。
- `redis_lianjie_shibai(caozuo: &str, cuowu: &str)`
  - 记录Redis操作失败的日志。
- `redis_shezhi_huancun_shibai(ditu_id: &str, cuowu: &str)`
  - 记录设置Redis缓存失败的日志。
- `redis_huoqu_huancun_shibai(ditu_id: &str, cuowu: &str)`
  - 记录获取Redis缓存失败的日志。
- `redis_shanchu_huancun_shibai(ditu_id: &str, cuowu: &str)`
  - 记录删除Redis缓存失败的日志。
- `redis_jiancha_huancun_shibai(ditu_id: &str, cuowu: &str)`
  - 记录检查Redis缓存存在失败的日志。
- `redis_piliang_qingchu_shibai(cuowu: &str)`
  - 记录批量清除Redis缓存失败的日志。
- `redis_huoqu_tongji_shibai(cuowu: &str)`
  - 记录获取Redis统计信息失败的日志。
- `shujuku_chaxun_shibai(sql: &str, cuowu: &str)`
  - 记录数据库查询失败的日志。