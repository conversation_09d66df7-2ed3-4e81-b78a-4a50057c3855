# 地图列表查询 (`ditu_liebiao_chaxun`)

本文档详细说明了 `ditu_liebiao_chaxun` 结构体的功能和实现逻辑，该结构体用于处理游戏中的地图列表查询，包括分页、缓存和地图名称获取等功能。

## 结构体概述

`ditu_liebiao_chaxun` 结构体是地图数据查询的核心，负责与数据库和缓存系统交互，以获取格式化的地图列表数据。

### 结构体定义

```rust
pub struct ditu_liebiao_chaxun {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}
```

- `mysql_lianjie`: `mysql_lianjie_guanli` 类型的实例，用于管理和提供 MySQL 数据库连接。
- `redis_lianjie`: 可选的 `redis_lianjie_guanli` 实例，用于管理 Redis 连接。如果为 `None`，则禁用缓存功能。
- `redis_kongzhi`: 可选的 `ditu_redis_kongzhi` 实例，封装了对 Redis 的具体操作，如获取和设置缓存。

## 核心功能

### 1. 分页查询

系统支持对地图列表进行分页查询，以避免一次性加载大量数据。

- **`chaxun_ditu_liebiao_shuju`**: 此异步函数负责从数据库中获取指定页码和数量的地图数据。它通过 `LIMIT` 和 `OFFSET` 子句实现分页。
- **`chaxun_ditu_zongshu`**: 用于查询 `ditu_huizong` 表中的地图总数，这是计算总页数的依据。
- **`gouzao_fenlei_chaxun_sql`**: 动态构建 SQL 查询语句，包含了所有的地图分类字段。

### 2. 缓存处理

为了提升性能，查询结果会被缓存到 Redis 中。

- **`chaxun_ditu_liebiao`**: 这是对外暴露的主要查询方法。它首先尝试从 Redis 缓存中获取数据。
  - 如果缓存命中，则直接返回数据，并通过日志记录数据来源为“缓存”。
  - 如果缓存未命中，则调用 `chaxun_ditu_liebiao_shuju` 从数据库查询数据。
- **数据缓存**: 查询结果在返回给调用方之前，会被序列化为 JSON 字符串并存储到 Redis 中，以便后续请求使用。
- **`qingchu_ditu_liebiao_huancun`**: 提供一个公共方法，用于手动清除所有地图列表的 Redis 缓存，确保数据可以被刷新。

### 3. 地图名称获取

地图名称的获取具有优先级逻辑，以确保显示最准确的名称。

- **`chaxun_ditu_mingzi_from_name`**: 此函数会优先从 `ditu_name` 表中根据 `ditu_id` 查询地图名称。
- **备用名称**: 如果在 `ditu_name` 表中找不到对应的名称，系统会回退使用 `ditu_huizong` 表中存储的 `ditu_mingcheng` 字段。
- **默认名称**: 如果两个来源都无法获取到名称，则会使用“未知地图”作为默认名称。

### 4. 分类数据处理

- **`huoqu_fenlei_ziduan`**: 定义了所有可能的地图分类字段名，如 `leixing_town`, `fenlei_field` 等。
- **`chuli_fenlei_ziduan`**: 该方法处理从数据库查询到的分类字段。它会遍历所有预定义的分类字段，检查其值是否为 `"true"`（不区分大小写）。如果是，则将该分类标记为 `true` 并添加到返回的 `HashMap` 中。这样，前端可以清晰地知道每个地图属于哪些分类。

## 使用示例

可以通过两种方式实例化 `ditu_liebiao_chaxun`：

- **不带缓存**:
  ```rust
  let chaxun = ditu_liebiao_chaxun::new(mysql_lianjie);
  ```
- **带缓存**:
  ```rust
  let chaxun = ditu_liebiao_chaxun::new_with_redis(mysql_lianjie, redis_lianjie);
  ```

然后调用 `chaxun_ditu_liebiao` 方法执行查询：

```rust
let canshu = ditu_liebiao_chaxun_canshu {
    dangqian_ye: 1,
    mei_ye_shuliang: 20,
};
let jieguo = chaxun.chaxun_ditu_liebiao(canshu).await?;