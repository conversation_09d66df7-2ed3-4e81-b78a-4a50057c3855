# 地图数据结构文档

本文档详细说明了在 `ditushujujiegouti.rs` 文件中定义的与地图数据处理相关的各个数据结构。这些结构用于地图信息的查询、缓存、列表展示和分类筛选等功能。

---

## 核心数据结构

### 1. `ditu_ziduan_jieguo`

- **用途**: 表示对地图单个特定字段进行查询后返回的结果。
- **字段**:
  - `ditu_id: String` - 地图的唯一标识符。
  - `ziduan_ming: String` - 被查询的字段名称。
  - `ziduan_zhi: Option<String>` - 查询到的字段值。如果字段不存在或没有值，则为 `None`。

### 2. `ditu_quanbu_xinxi_jieguo`

- **用途**: 存储从数据库或缓存中获取到的某张地图的全部信息。
- **字段**:
  - `ditu_id: String` - 地图的唯一标识符。
  - `suoyou_ziduan: HashMap<String, String>` - 一个包含该地图所有字段及其对应值的哈希图（HashMap）。
  - `shuju_laiyuan: String` - 标明数据来源，值为 "缓存" 或 "数据库"。

### 3. `ditu_huancun_caozuo_jieguo`

- **用途**: 表示对地图数据进行缓存操作（如更新、删除）后的结果。
- **字段**:
  - `ditu_id: String` - 被操作的地图的唯一标识符。
  - `chenggong: bool` - 操作是否成功。`true` 表示成功，`false` 表示失败。
  - `xiaoxi: String` - 提供关于操作结果的附加信息，例如成功消息或错误详情。

### 4. `ditu_chaxun_canshu`

- **用途**: 定义了在查询地图数据时需要提供的参数。
- **字段**:
  - `ditu_id: String` - 目标地图的唯一标识符。
  - `ziduan_ming: String` - 需要查询的字段名。如果值为 `"quanbu_xinxi"`，则表示需要获取该地图的全部信息。

---

## 地图列表相关结构

### 5. `ditu_liebiao_xiangmu`

- **用途**: 代表在地图列表中显示的单个地图条目。
- **字段**:
  - `ditu_id: String` - 地图的唯一标识符。
  - `ditu_mingzi: String` - 地图的名称。
  - `ditu_fenlei: HashMap<String, bool>` - 包含地图所有分类标签的哈希图，键（key）是分类名，值（value）固定为 `true`。

### 6. `ditu_liebiao_chaxun_canshu`

- **用途**: 用于获取地图列表时的分页参数。
- **字段**:
  - `mei_ye_shuliang: u32` - 每页显示的地图数量。
  - `dangqian_ye: u32` - 请求的当前页码（从1开始）。

### 7. `ditu_liebiao_jieguo`

- **用途**: 包含地图列表查询的结果以及完整的分页信息。
- **字段**:
  - `ditu_liebiao: Vec<ditu_liebiao_xiangmu>` - 当前页的地图列表。
  - `dangqian_ye: u32` - 当前所在的页码。
  - `quanbu_ye_shu: u32` - 根据总数和每页数量计算出的总页数。
  - `zonggong_ditu_shuliang: u32` - 数据库中地图的总数量。
  - `dangqian_ye_ditu_shuliang: u32` - 当前页实际返回的地图数量。

---

## 地图分类查询相关结构

### 8. `ditu_fenlei_tiaojian`

- **用途**: 定义一个具体的地图分类筛选条件。
- **字段**:
  - `ziduan_ming: String` - 分类字段的名称（例如 `"leixing_town"`, `"fenlei_field"`）。
  - `zhi: bool` - 期望该分类字段的值，通常为 `true`。

### 9. `ditu_fenlei_chaxun_canshu`

- **用途**: 封装了按分类筛选地图时的所有查询参数，包括筛选条件和分页。
- **字段**:
  - `mei_ye_shuliang: u32` - 每页显示的地图数量。
  - `dangqian_ye: u32` - 请求的当前页码（从1开始）。
  - `fenlei_tiaojian: Vec<ditu_fenlei_tiaojian>` - 一个包含多个筛选条件的向量（Vector）。
  - `luoji_guanxi: String` - 多个筛选条件之间的逻辑关系，值为 `"AND"` 或 `"OR"`。